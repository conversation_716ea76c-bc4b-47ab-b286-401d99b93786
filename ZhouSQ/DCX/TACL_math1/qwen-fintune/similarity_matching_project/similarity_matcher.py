"""
相似度匹配器 - 核心匹配逻辑
"""

import numpy as np
from typing import List, Dict, Tuple, Optional
import faiss
from embedding_model import EmbeddingModel
from data_loader import DataLoader
import config
import json

class SimilarityMatcher:
    def __init__(self):
        self.embedding_model = EmbeddingModel()
        self.data_loader = DataLoader()
        
        # 训练数据
        self.train_texts = None
        self.train_labels = None
        self.train_embeddings = None
        
        # FAISS索引用于快速相似度搜索
        self.faiss_index = None
        
    def build_knowledge_base(self, use_cache: bool = True):
        """
        构建知识库 - 计算所有训练样本的嵌入向量
        """
        print("正在构建知识库...")

        # 加载训练数据
        self.train_texts, self.train_labels = self.data_loader.create_training_dataset()
        print(f"训练数据大小: {len(self.train_texts)}")

        # 尝试从缓存加载嵌入向量
        cache_name = "train_embeddings"
        if use_cache:
            cached_embeddings, cached_texts = self.embedding_model.load_embeddings_cache(cache_name)
            if cached_embeddings is not None and len(cached_texts) == len(self.train_texts):
                self.train_embeddings = cached_embeddings
                print("使用缓存的嵌入向量")
            else:
                print("缓存不匹配，重新计算嵌入向量")
                use_cache = False

        # 如果没有缓存或缓存无效，重新计算
        if not use_cache or self.train_embeddings is None:
            self.train_embeddings = self.embedding_model.encode_texts(self.train_texts)
            # 保存到缓存
            self.embedding_model.save_embeddings_cache(
                self.train_embeddings,
                self.train_texts,
                cache_name
            )

        # 构建FAISS索引用于快速搜索
        self._build_faiss_index()

        print("知识库构建完成!")

    def add_new_data(self, new_texts: List[str], new_labels: List[List[str]], save_cache: bool = True):
        """
        增量添加新数据到知识库
        """
        print(f"正在添加 {len(new_texts)} 条新数据...")

        # 计算新数据的嵌入向量
        new_embeddings = self.embedding_model.encode_texts(new_texts)

        # 合并数据
        if self.train_texts is None:
            self.train_texts = new_texts
            self.train_labels = new_labels
            self.train_embeddings = new_embeddings
        else:
            self.train_texts.extend(new_texts)
            self.train_labels.extend(new_labels)
            self.train_embeddings = np.vstack([self.train_embeddings, new_embeddings])

        print(f"知识库更新后大小: {len(self.train_texts)}")

        # 重新构建FAISS索引
        self._build_faiss_index()

        # 保存更新后的缓存
        if save_cache:
            cache_name = "train_embeddings"
            self.embedding_model.save_embeddings_cache(
                self.train_embeddings,
                self.train_texts,
                cache_name
            )
            print("缓存已更新")

        print("新数据添加完成!")

    def add_data_from_file(self, file_path: str, save_cache: bool = True):
        """
        从文件添加新数据
        """
        print(f"从文件添加数据: {file_path}")

        # 加载新数据
        new_data = self.data_loader.load_json_data(file_path)
        new_texts, new_labels = self.data_loader.get_doc_tokens_and_labels(new_data)

        # 添加到知识库
        self.add_new_data(new_texts, new_labels, save_cache)

    def add_new_data(self, new_texts: List[str], new_labels: List[List[str]], save_cache: bool = True):
        """
        增量添加新数据到知识库
        """
        print(f"正在添加 {len(new_texts)} 条新数据...")

        # 计算新数据的嵌入向量
        new_embeddings = self.embedding_model.encode_texts(new_texts)

        # 合并数据
        if self.train_texts is None:
            self.train_texts = new_texts
            self.train_labels = new_labels
            self.train_embeddings = new_embeddings
        else:
            self.train_texts.extend(new_texts)
            self.train_labels.extend(new_labels)
            self.train_embeddings = np.vstack([self.train_embeddings, new_embeddings])

        print(f"知识库更新后大小: {len(self.train_texts)}")

        # 重新构建FAISS索引
        self._build_faiss_index()

        # 保存更新后的缓存
        if save_cache:
            cache_name = "train_embeddings"
            self.embedding_model.save_embeddings_cache(
                self.train_embeddings,
                self.train_texts,
                cache_name
            )
            print("缓存已更新")

        print("新数据添加完成!")

    def add_data_from_file(self, file_path: str, save_cache: bool = True):
        """
        从文件添加新数据
        """
        print(f"从文件添加数据: {file_path}")

        # 加载新数据
        new_data = self.data_loader.load_json_data(file_path)
        new_texts, new_labels = self.data_loader.get_doc_tokens_and_labels(new_data)

        # 添加到知识库
        self.add_new_data(new_texts, new_labels, save_cache)

    def save_current_knowledge_base(self, filename: str = None):
        """
        保存当前知识库状态
        """
        if filename is None:
            from datetime import datetime
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"knowledge_base_backup_{timestamp}.pkl"

        if self.train_embeddings is not None:
            # 对于备份，使用特殊的文件名格式
            backup_name = filename.replace('.pkl', '').replace('knowledge_base_backup_', '')
            self.embedding_model.save_embeddings_cache(
                self.train_embeddings,
                self.train_texts,
                f"backup_{backup_name}"
            )
            print(f"知识库已保存为: backup_{backup_name}.pkl")
        else:
            print("知识库为空，无法保存")
    
    def _build_faiss_index(self):
        """构建FAISS索引"""
        dimension = self.train_embeddings.shape[1]
        
        # 使用内积搜索（因为向量已归一化，内积等于余弦相似度）
        self.faiss_index = faiss.IndexFlatIP(dimension)
        self.faiss_index.add(self.train_embeddings.astype('float32'))
        
        print(f"FAISS索引构建完成，维度: {dimension}, 样本数: {self.faiss_index.ntotal}")
    
    def find_most_similar(self, query_text: str, top_k: int = None) -> List[Dict]:
        """
        找到最相似的训练样本
        """
        if self.train_embeddings is None:
            raise ValueError("知识库未构建，请先调用build_knowledge_base()")
        
        top_k = top_k or config.TOP_K_SIMILAR
        
        # 编码查询文本 - 单次查询不显示进度条
        query_embedding = self.embedding_model.encode_texts([query_text], show_progress=False)
        
        # 使用FAISS搜索最相似的样本
        similarities, indices = self.faiss_index.search(
            query_embedding.astype('float32'), 
            top_k
        )
        
        results = []
        for i, (similarity, idx) in enumerate(zip(similarities[0], indices[0])):
            if idx < len(self.train_texts):  # 确保索引有效
                result = {
                    'rank': i + 1,
                    'similarity': float(similarity),
                    'matched_text': self.train_texts[idx],
                    'matched_label': self.train_labels[idx],
                    'index': int(idx)
                }
                results.append(result)
        
        return results
    
    def predict_label(self, query_text: str, verbose: bool = False) -> List[str]:
        """预测查询文本的标签（返回最相似样本的标签）"""
        similar_samples = self.find_most_similar(query_text, top_k=1)

        if similar_samples:
            most_similar = similar_samples[0]
            if verbose:
                print(f"最相似样本相似度: {most_similar['similarity']:.4f}")
            return most_similar['matched_label']
        else:
            if verbose:
                print("未找到相似样本")
            return []
    
    def batch_predict(self, query_texts: List[str]) -> List[Dict]:
        """
        批量预测
        """
        results = []
        
        for i, query_text in enumerate(query_texts):
            print(f"处理查询 {i+1}/{len(query_texts)}")
            
            predicted_label = self.predict_label(query_text)
            similar_samples = self.find_most_similar(query_text, top_k=1)
            
            result = {
                'query_text': query_text,
                'predicted_label': predicted_label,
                'most_similar_info': similar_samples[0] if similar_samples else None
            }
            results.append(result)
        
        return results
    
    def evaluate_on_test_set(self) -> Dict:
        """在测试集上评估性能"""
        from tqdm import tqdm

        test_texts, test_labels = self.data_loader.create_test_dataset()
        print(f"测试集大小: {len(test_texts)}")

        correct_predictions = 0
        total_predictions = len(test_texts)
        detailed_results = []

        # 使用统一的进度条
        progress_bar = tqdm(
            zip(test_texts, test_labels),
            total=total_predictions,
            desc="相似度评估",
            leave=True
        )

        for test_text, true_label in progress_bar:
            predicted_label = self.predict_label(test_text)

            # 检查预测是否完全匹配
            is_correct = predicted_label == true_label
            if is_correct:
                correct_predictions += 1

            # 更新进度条显示
            accuracy_so_far = correct_predictions / len(detailed_results) if detailed_results else 0
            progress_bar.set_postfix({
                '准确率': f'{accuracy_so_far:.3f}',
                '正确': correct_predictions
            })

            detailed_results.append({
                'test_text': test_text,
                'true_label': true_label,
                'predicted_label': predicted_label,
                'is_correct': is_correct
            })
        
        accuracy = correct_predictions / total_predictions
        
        evaluation_result = {
            'accuracy': accuracy,
            'correct_predictions': correct_predictions,
            'total_predictions': total_predictions,
            'detailed_results': detailed_results
        }
        
        return evaluation_result
