"""
数学分类系统核心类 - 统一的系统入口
"""

import json
import logging
from typing import List, Dict, Tuple, Optional
from pathlib import Path

from .config import Config
from .vector_builder import VectorBuilder
from .model_trainer import ModelTrainer
from .model_evaluator import ModelEvaluator
from .predictor import Predictor

logger = logging.getLogger(__name__)

class MathClassificationSystem:
    """
    数学分类系统主类
    
    功能：
    1. 向量构建 - 构建训练数据的向量表示
    2. 模型微调 - 使用LoRA微调分类模型
    3. 模型评估 - 评估系统性能
    4. 预测生成 - 根据阈值自动选择最佳策略进行预测
    """
    
    def __init__(self, config: Config = None):
        """初始化系统"""
        self.config = config or Config()

        # 初始化各个模块
        self.vector_builder = VectorBuilder(self.config)
        self.model_trainer = ModelTrainer(self.config)
        self.model_evaluator = ModelEvaluator(self.config)
        self.predictor = Predictor(self.config)

        # 检查现有缓存
        self.cache_info = self.config.check_existing_cache()

        # 系统状态 - 初始化时先设为False，实际加载后再更新
        self.vector_ready = False
        self.model_ready = False

        if self.cache_info['vector_cache_exists']:
            logger.info(f"发现现有向量缓存: {self.cache_info['vector_cache_path']}")
        if self.cache_info['model_cache_exists']:
            logger.info(f"发现现有模型缓存: {self.cache_info['model_cache_path']}")

        logger.info("数学分类系统初始化完成")
    
    def build_vectors(self, use_cache: bool = True, force_rebuild: bool = False) -> bool:
        """
        构建向量知识库

        Args:
            use_cache: 是否使用缓存
            force_rebuild: 是否强制重建

        Returns:
            bool: 是否成功构建
        """
        try:
            # 如果有现有缓存且不强制重建，优先使用缓存
            if not force_rebuild and self.cache_info['vector_cache_exists']:
                logger.info("使用现有向量缓存...")
                success = self.vector_builder.build_vectors(use_cache=True, force_rebuild=False)
            else:
                logger.info("开始构建向量知识库...")
                success = self.vector_builder.build_vectors(use_cache=use_cache, force_rebuild=force_rebuild)

            self.vector_ready = success

            if success:
                logger.info("向量知识库构建成功")
            else:
                logger.error("向量知识库构建失败")

            return success
        except Exception as e:
            logger.error(f"构建向量知识库时出错: {e}")
            return False
    
    def train_model(self, force_retrain: bool = False) -> bool:
        """
        训练分类模型

        Args:
            force_retrain: 是否强制重新训练

        Returns:
            bool: 是否成功训练
        """
        try:
            # 如果有现有模型且不强制重训练，优先加载现有模型
            if not force_retrain and self.cache_info['model_cache_exists']:
                logger.info("使用现有模型...")
                success = self.model_trainer.train_model(force_retrain=False)
            else:
                logger.info("开始训练分类模型...")
                success = self.model_trainer.train_model(force_retrain=force_retrain)

            self.model_ready = success

            if success:
                logger.info("分类模型训练成功")
            else:
                logger.error("分类模型训练失败")

            return success
        except Exception as e:
            logger.error(f"训练分类模型时出错: {e}")
            return False
    
    def evaluate_system(self, test_data_path: str = None) -> Dict:
        """
        评估系统性能
        
        Args:
            test_data_path: 测试数据路径，默认使用配置中的测试数据
            
        Returns:
            Dict: 评估结果
        """
        try:
            logger.info("开始评估系统性能...")
            
            # 确保系统已准备就绪
            if not self.vector_ready:
                logger.warning("向量知识库未准备就绪，尝试构建...")
                if not self.build_vectors():
                    raise RuntimeError("无法构建向量知识库")
            
            if not self.model_ready:
                logger.warning("分类模型未准备就绪，尝试训练...")
                if not self.train_model():
                    raise RuntimeError("无法训练分类模型")
            
            # 执行评估
            results = self.model_evaluator.evaluate(
                vector_builder=self.vector_builder,
                model_trainer=self.model_trainer,
                test_data_path=test_data_path
            )
            
            logger.info("系统评估完成")
            return results
            
        except Exception as e:
            logger.error(f"评估系统时出错: {e}")
            return {"error": str(e)}
    
    def predict(self, query: str, return_details: bool = False) -> Dict:
        """
        预测单个查询
        
        Args:
            query: 查询文本
            return_details: 是否返回详细信息
            
        Returns:
            Dict: 预测结果
        """
        try:
            # 确保系统已准备就绪
            if not self.vector_ready:
                logger.warning("向量知识库未准备就绪，尝试构建...")
                if not self.build_vectors():
                    raise RuntimeError("无法构建向量知识库")
            
            if not self.model_ready:
                logger.warning("分类模型未准备就绪，尝试训练...")
                if not self.train_model():
                    raise RuntimeError("无法训练分类模型")
            
            # 执行预测
            result = self.predictor.predict(
                query=query,
                vector_builder=self.vector_builder,
                model_trainer=self.model_trainer,
                return_details=return_details
            )
            
            return result
            
        except Exception as e:
            logger.error(f"预测时出错: {e}")
            return {"error": str(e), "prediction": None}
    
    def batch_predict(self, queries: List[str], return_details: bool = False) -> List[Dict]:
        """
        批量预测
        
        Args:
            queries: 查询文本列表
            return_details: 是否返回详细信息
            
        Returns:
            List[Dict]: 预测结果列表
        """
        results = []
        for i, query in enumerate(queries):
            if i % 10 == 0:
                logger.info(f"批量预测进度: {i+1}/{len(queries)}")
            
            result = self.predict(query, return_details=return_details)
            results.append(result)
        
        return results
    
    def get_system_status(self) -> Dict:
        """获取系统状态"""
        return {
            "vector_ready": self.vector_ready,
            "model_ready": self.model_ready,
            "similarity_threshold": self.config.SIMILARITY_THRESHOLD,
            "device": self.config.DEVICE,
            "use_lora": self.config.USE_LORA
        }
