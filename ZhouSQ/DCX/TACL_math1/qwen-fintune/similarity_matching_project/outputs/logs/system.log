2025-09-01 16:49:19,731 - __main__ - INFO - 检查运行环境...
2025-09-01 16:49:24,602 - __main__ - ERROR - 缺少依赖包: scikit-learn, faiss-cpu
2025-09-01 16:49:24,602 - __main__ - ERROR - 请运行: pip install -r requirements.txt
2025-09-01 16:55:06,966 - __main__ - INFO - 检查运行环境...
2025-09-01 16:55:11,599 - __main__ - ERROR - 缺少依赖包: scikit-learn, faiss-cpu
2025-09-01 16:55:11,600 - __main__ - ERROR - 请运行: pip install -r requirements.txt
2025-09-01 16:55:34,971 - __main__ - INFO - 检查运行环境...
2025-09-01 16:55:39,811 - __main__ - INFO - 环境验证通过
2025-09-01 16:55:39,811 - __main__ - INFO - 开始训练分类模型...
2025-09-01 16:55:39,814 - faiss.loader - INFO - Loading faiss with AVX512 support.
2025-09-01 16:55:39,832 - faiss.loader - INFO - Successfully loaded faiss with AVX512 support.
2025-09-01 16:55:39,867 - __main__ - ERROR - 运行出错: module 'config' has no attribute 'QWEN_MODEL_PATH'
2025-09-01 17:01:34,974 - __main__ - INFO - 检查运行环境...
2025-09-01 17:01:39,780 - __main__ - INFO - 环境验证通过
2025-09-01 17:01:39,780 - __main__ - INFO - 开始训练分类模型...
2025-09-01 17:01:39,781 - faiss.loader - INFO - Loading faiss with AVX512 support.
2025-09-01 17:01:39,798 - faiss.loader - INFO - Successfully loaded faiss with AVX512 support.
2025-09-01 17:01:40,844 - embedding_model - WARNING - 缓存配置不匹配，将重新计算
2025-09-01 17:01:40,873 - embedding_model - INFO - 加载嵌入模型: /home/<USER>/ZhouSQ/DCX/TACL_math1/Qwen3-Embedding-4B
2025-09-01 17:01:51,263 - embedding_model - INFO - 嵌入模型加载完成
2025-09-01 17:04:47,871 - __main__ - INFO - 检查运行环境...
2025-09-01 17:04:52,593 - __main__ - INFO - 环境验证通过
2025-09-01 17:04:52,593 - __main__ - INFO - 开始训练分类模型...
2025-09-01 17:04:53,250 - __main__ - INFO - 训练数据: 48211 个样本
2025-09-01 17:04:53,250 - __main__ - INFO - 验证数据: 5073 个样本
2025-09-01 17:04:53,250 - __main__ - INFO - 唯一标签数量: 2302
2025-09-01 17:05:03,647 - __main__ - ERROR - 运行出错: TrainingArguments.__init__() got an unexpected keyword argument 'evaluation_strategy'
2025-09-01 17:05:47,300 - __main__ - INFO - 检查运行环境...
2025-09-01 17:05:52,318 - __main__ - INFO - 环境验证通过
2025-09-01 17:05:52,318 - __main__ - INFO - 开始训练分类模型...
2025-09-01 17:05:53,017 - __main__ - INFO - 训练数据: 48211 个样本
2025-09-01 17:05:53,017 - __main__ - INFO - 验证数据: 5073 个样本
2025-09-01 17:05:53,017 - __main__ - INFO - 唯一标签数量: 2302
2025-09-01 17:06:03,944 - root - INFO - gcc -pthread -B /home/<USER>/anaconda3/envs/htc/compiler_compat -fno-strict-overflow -Wsign-compare -DNDEBUG -O2 -Wall -fPIC -O2 -isystem /home/<USER>/anaconda3/envs/htc/include -fPIC -O2 -isystem /home/<USER>/anaconda3/envs/htc/include -fPIC -c /tmp/tmpreoqw5nd/test.c -o /tmp/tmpreoqw5nd/test.o
2025-09-01 17:06:03,964 - root - INFO - gcc -pthread -B /home/<USER>/anaconda3/envs/htc/compiler_compat /tmp/tmpreoqw5nd/test.o -laio -o /tmp/tmpreoqw5nd/a.out
2025-09-01 17:06:04,471 - root - INFO - gcc -pthread -B /home/<USER>/anaconda3/envs/htc/compiler_compat -fno-strict-overflow -Wsign-compare -DNDEBUG -O2 -Wall -fPIC -O2 -isystem /home/<USER>/anaconda3/envs/htc/include -fPIC -O2 -isystem /home/<USER>/anaconda3/envs/htc/include -fPIC -c /tmp/tmp4dk_d9k7/test.c -o /tmp/tmp4dk_d9k7/test.o
2025-09-01 17:06:04,490 - root - INFO - gcc -pthread -B /home/<USER>/anaconda3/envs/htc/compiler_compat /tmp/tmp4dk_d9k7/test.o -L/usr/local/cuda -L/usr/local/cuda/lib64 -lcufile -o /tmp/tmp4dk_d9k7/a.out
2025-09-01 17:06:04,562 - root - INFO - gcc -pthread -B /home/<USER>/anaconda3/envs/htc/compiler_compat -fno-strict-overflow -Wsign-compare -DNDEBUG -O2 -Wall -fPIC -O2 -isystem /home/<USER>/anaconda3/envs/htc/include -fPIC -O2 -isystem /home/<USER>/anaconda3/envs/htc/include -fPIC -c /tmp/tmpwik_h04n/test.c -o /tmp/tmpwik_h04n/test.o
2025-09-01 17:06:04,585 - root - INFO - gcc -pthread -B /home/<USER>/anaconda3/envs/htc/compiler_compat /tmp/tmpwik_h04n/test.o -laio -o /tmp/tmpwik_h04n/a.out
2025-09-01 17:06:06,684 - __main__ - ERROR - 运行出错: NCCL Error 5: invalid usage (run with NCCL_DEBUG=WARN for details)
2025-09-01 17:06:50,582 - __main__ - INFO - 检查运行环境...
2025-09-01 17:06:55,761 - __main__ - INFO - 环境验证通过
2025-09-01 17:06:55,761 - __main__ - INFO - 开始训练分类模型...
2025-09-01 17:06:56,454 - __main__ - INFO - 训练数据: 48211 个样本
2025-09-01 17:06:56,454 - __main__ - INFO - 验证数据: 5073 个样本
2025-09-01 17:06:56,454 - __main__ - INFO - 唯一标签数量: 2302
2025-09-01 17:07:06,811 - root - INFO - gcc -pthread -B /home/<USER>/anaconda3/envs/htc/compiler_compat -fno-strict-overflow -Wsign-compare -DNDEBUG -O2 -Wall -fPIC -O2 -isystem /home/<USER>/anaconda3/envs/htc/include -fPIC -O2 -isystem /home/<USER>/anaconda3/envs/htc/include -fPIC -c /tmp/tmpmk7ra65v/test.c -o /tmp/tmpmk7ra65v/test.o
2025-09-01 17:07:06,832 - root - INFO - gcc -pthread -B /home/<USER>/anaconda3/envs/htc/compiler_compat /tmp/tmpmk7ra65v/test.o -laio -o /tmp/tmpmk7ra65v/a.out
2025-09-01 17:07:07,391 - root - INFO - gcc -pthread -B /home/<USER>/anaconda3/envs/htc/compiler_compat -fno-strict-overflow -Wsign-compare -DNDEBUG -O2 -Wall -fPIC -O2 -isystem /home/<USER>/anaconda3/envs/htc/include -fPIC -O2 -isystem /home/<USER>/anaconda3/envs/htc/include -fPIC -c /tmp/tmpea3e7qwh/test.c -o /tmp/tmpea3e7qwh/test.o
2025-09-01 17:07:07,412 - root - INFO - gcc -pthread -B /home/<USER>/anaconda3/envs/htc/compiler_compat /tmp/tmpea3e7qwh/test.o -L/usr/local/cuda -L/usr/local/cuda/lib64 -lcufile -o /tmp/tmpea3e7qwh/a.out
2025-09-01 17:07:07,482 - root - INFO - gcc -pthread -B /home/<USER>/anaconda3/envs/htc/compiler_compat -fno-strict-overflow -Wsign-compare -DNDEBUG -O2 -Wall -fPIC -O2 -isystem /home/<USER>/anaconda3/envs/htc/include -fPIC -O2 -isystem /home/<USER>/anaconda3/envs/htc/include -fPIC -c /tmp/tmp_n9eemi3/test.c -o /tmp/tmp_n9eemi3/test.o
2025-09-01 17:07:07,499 - root - INFO - gcc -pthread -B /home/<USER>/anaconda3/envs/htc/compiler_compat /tmp/tmp_n9eemi3/test.o -laio -o /tmp/tmp_n9eemi3/a.out
2025-09-01 17:07:10,025 - __main__ - ERROR - 运行出错: CUDA out of memory. Tried to allocate 152.00 MiB. GPU 0 has a total capacity of 39.50 GiB of which 69.88 MiB is free. Including non-PyTorch memory, this process has 39.41 GiB memory in use. Of the allocated memory 38.72 GiB is allocated by PyTorch, and 206.71 MiB is reserved by PyTorch but unallocated. If reserved but unallocated memory is large try setting PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True to avoid fragmentation.  See documentation for Memory Management  (https://pytorch.org/docs/stable/notes/cuda.html#environment-variables)
2025-09-01 17:08:22,424 - __main__ - INFO - 检查运行环境...
2025-09-01 17:08:28,036 - __main__ - INFO - 环境验证通过
2025-09-01 17:08:28,036 - __main__ - INFO - 开始训练分类模型...
2025-09-01 17:08:28,766 - __main__ - INFO - 训练数据: 48211 个样本
2025-09-01 17:08:28,766 - __main__ - INFO - 验证数据: 5073 个样本
2025-09-01 17:08:28,766 - __main__ - INFO - 唯一标签数量: 2302
2025-09-01 17:08:39,775 - root - INFO - gcc -pthread -B /home/<USER>/anaconda3/envs/htc/compiler_compat -fno-strict-overflow -Wsign-compare -DNDEBUG -O2 -Wall -fPIC -O2 -isystem /home/<USER>/anaconda3/envs/htc/include -fPIC -O2 -isystem /home/<USER>/anaconda3/envs/htc/include -fPIC -c /tmp/tmp8j_5k6b4/test.c -o /tmp/tmp8j_5k6b4/test.o
2025-09-01 17:08:39,796 - root - INFO - gcc -pthread -B /home/<USER>/anaconda3/envs/htc/compiler_compat /tmp/tmp8j_5k6b4/test.o -laio -o /tmp/tmp8j_5k6b4/a.out
2025-09-01 17:08:40,364 - root - INFO - gcc -pthread -B /home/<USER>/anaconda3/envs/htc/compiler_compat -fno-strict-overflow -Wsign-compare -DNDEBUG -O2 -Wall -fPIC -O2 -isystem /home/<USER>/anaconda3/envs/htc/include -fPIC -O2 -isystem /home/<USER>/anaconda3/envs/htc/include -fPIC -c /tmp/tmp255m8ltx/test.c -o /tmp/tmp255m8ltx/test.o
2025-09-01 17:08:40,385 - root - INFO - gcc -pthread -B /home/<USER>/anaconda3/envs/htc/compiler_compat /tmp/tmp255m8ltx/test.o -L/usr/local/cuda -L/usr/local/cuda/lib64 -lcufile -o /tmp/tmp255m8ltx/a.out
2025-09-01 17:08:40,461 - root - INFO - gcc -pthread -B /home/<USER>/anaconda3/envs/htc/compiler_compat -fno-strict-overflow -Wsign-compare -DNDEBUG -O2 -Wall -fPIC -O2 -isystem /home/<USER>/anaconda3/envs/htc/include -fPIC -O2 -isystem /home/<USER>/anaconda3/envs/htc/include -fPIC -c /tmp/tmpwm1cn47a/test.c -o /tmp/tmpwm1cn47a/test.o
2025-09-01 17:08:40,481 - root - INFO - gcc -pthread -B /home/<USER>/anaconda3/envs/htc/compiler_compat /tmp/tmpwm1cn47a/test.o -laio -o /tmp/tmpwm1cn47a/a.out
2025-09-01 17:08:42,651 - __main__ - ERROR - 运行出错: Cannot handle batch sizes > 1 if no padding token is defined.
2025-09-01 17:09:13,566 - __main__ - INFO - 检查运行环境...
2025-09-01 17:09:18,767 - __main__ - INFO - 环境验证通过
2025-09-01 17:09:18,767 - __main__ - INFO - 开始训练分类模型...
2025-09-01 17:09:19,461 - __main__ - INFO - 训练数据: 48211 个样本
2025-09-01 17:09:19,461 - __main__ - INFO - 验证数据: 5073 个样本
2025-09-01 17:09:19,461 - __main__ - INFO - 唯一标签数量: 2302
2025-09-01 17:09:30,482 - root - INFO - gcc -pthread -B /home/<USER>/anaconda3/envs/htc/compiler_compat -fno-strict-overflow -Wsign-compare -DNDEBUG -O2 -Wall -fPIC -O2 -isystem /home/<USER>/anaconda3/envs/htc/include -fPIC -O2 -isystem /home/<USER>/anaconda3/envs/htc/include -fPIC -c /tmp/tmpk4kcyx8_/test.c -o /tmp/tmpk4kcyx8_/test.o
2025-09-01 17:09:30,502 - root - INFO - gcc -pthread -B /home/<USER>/anaconda3/envs/htc/compiler_compat /tmp/tmpk4kcyx8_/test.o -laio -o /tmp/tmpk4kcyx8_/a.out
2025-09-01 17:09:31,008 - root - INFO - gcc -pthread -B /home/<USER>/anaconda3/envs/htc/compiler_compat -fno-strict-overflow -Wsign-compare -DNDEBUG -O2 -Wall -fPIC -O2 -isystem /home/<USER>/anaconda3/envs/htc/include -fPIC -O2 -isystem /home/<USER>/anaconda3/envs/htc/include -fPIC -c /tmp/tmpz4l3m86z/test.c -o /tmp/tmpz4l3m86z/test.o
2025-09-01 17:09:31,028 - root - INFO - gcc -pthread -B /home/<USER>/anaconda3/envs/htc/compiler_compat /tmp/tmpz4l3m86z/test.o -L/usr/local/cuda -L/usr/local/cuda/lib64 -lcufile -o /tmp/tmpz4l3m86z/a.out
2025-09-01 17:09:31,098 - root - INFO - gcc -pthread -B /home/<USER>/anaconda3/envs/htc/compiler_compat -fno-strict-overflow -Wsign-compare -DNDEBUG -O2 -Wall -fPIC -O2 -isystem /home/<USER>/anaconda3/envs/htc/include -fPIC -O2 -isystem /home/<USER>/anaconda3/envs/htc/include -fPIC -c /tmp/tmpj8c6ubpi/test.c -o /tmp/tmpj8c6ubpi/test.o
2025-09-01 17:09:31,116 - root - INFO - gcc -pthread -B /home/<USER>/anaconda3/envs/htc/compiler_compat /tmp/tmpj8c6ubpi/test.o -laio -o /tmp/tmpj8c6ubpi/a.out
2025-09-01 17:09:32,884 - __main__ - ERROR - 运行出错: Cannot handle batch sizes > 1 if no padding token is defined.
2025-09-01 17:10:12,057 - __main__ - INFO - 检查运行环境...
2025-09-01 17:10:16,871 - __main__ - INFO - 环境验证通过
2025-09-01 17:10:16,871 - __main__ - INFO - 开始训练分类模型...
2025-09-01 17:10:17,543 - __main__ - INFO - 训练数据: 48211 个样本
2025-09-01 17:10:17,543 - __main__ - INFO - 验证数据: 5073 个样本
2025-09-01 17:10:17,543 - __main__ - INFO - 唯一标签数量: 2302
2025-09-01 17:10:27,538 - root - INFO - gcc -pthread -B /home/<USER>/anaconda3/envs/htc/compiler_compat -fno-strict-overflow -Wsign-compare -DNDEBUG -O2 -Wall -fPIC -O2 -isystem /home/<USER>/anaconda3/envs/htc/include -fPIC -O2 -isystem /home/<USER>/anaconda3/envs/htc/include -fPIC -c /tmp/tmp5gxkfca9/test.c -o /tmp/tmp5gxkfca9/test.o
2025-09-01 17:10:27,556 - root - INFO - gcc -pthread -B /home/<USER>/anaconda3/envs/htc/compiler_compat /tmp/tmp5gxkfca9/test.o -laio -o /tmp/tmp5gxkfca9/a.out
2025-09-01 17:10:28,071 - root - INFO - gcc -pthread -B /home/<USER>/anaconda3/envs/htc/compiler_compat -fno-strict-overflow -Wsign-compare -DNDEBUG -O2 -Wall -fPIC -O2 -isystem /home/<USER>/anaconda3/envs/htc/include -fPIC -O2 -isystem /home/<USER>/anaconda3/envs/htc/include -fPIC -c /tmp/tmpmhk7ja1q/test.c -o /tmp/tmpmhk7ja1q/test.o
2025-09-01 17:10:28,090 - root - INFO - gcc -pthread -B /home/<USER>/anaconda3/envs/htc/compiler_compat /tmp/tmpmhk7ja1q/test.o -L/usr/local/cuda -L/usr/local/cuda/lib64 -lcufile -o /tmp/tmpmhk7ja1q/a.out
2025-09-01 17:10:28,163 - root - INFO - gcc -pthread -B /home/<USER>/anaconda3/envs/htc/compiler_compat -fno-strict-overflow -Wsign-compare -DNDEBUG -O2 -Wall -fPIC -O2 -isystem /home/<USER>/anaconda3/envs/htc/include -fPIC -O2 -isystem /home/<USER>/anaconda3/envs/htc/include -fPIC -c /tmp/tmpjaw9ttsv/test.c -o /tmp/tmpjaw9ttsv/test.o
2025-09-01 17:10:28,181 - root - INFO - gcc -pthread -B /home/<USER>/anaconda3/envs/htc/compiler_compat /tmp/tmpjaw9ttsv/test.o -laio -o /tmp/tmpjaw9ttsv/a.out
2025-09-01 17:18:33,770 - __main__ - INFO - 检查运行环境...
2025-09-01 17:18:38,973 - __main__ - INFO - 环境验证通过
2025-09-01 17:18:38,974 - __main__ - INFO - 开始训练分类模型...
2025-09-01 17:18:39,651 - __main__ - INFO - 训练数据: 48211 个样本
2025-09-01 17:18:39,651 - __main__ - INFO - 验证数据: 5073 个样本
2025-09-01 17:18:39,651 - __main__ - INFO - 唯一标签数量: 2302
2025-09-01 17:18:47,230 - root - INFO - gcc -pthread -B /home/<USER>/anaconda3/envs/htc/compiler_compat -fno-strict-overflow -Wsign-compare -DNDEBUG -O2 -Wall -fPIC -O2 -isystem /home/<USER>/anaconda3/envs/htc/include -fPIC -O2 -isystem /home/<USER>/anaconda3/envs/htc/include -fPIC -c /tmp/tmp7gr8udpk/test.c -o /tmp/tmp7gr8udpk/test.o
2025-09-01 17:18:47,250 - root - INFO - gcc -pthread -B /home/<USER>/anaconda3/envs/htc/compiler_compat /tmp/tmp7gr8udpk/test.o -laio -o /tmp/tmp7gr8udpk/a.out
2025-09-01 17:18:47,722 - root - INFO - gcc -pthread -B /home/<USER>/anaconda3/envs/htc/compiler_compat -fno-strict-overflow -Wsign-compare -DNDEBUG -O2 -Wall -fPIC -O2 -isystem /home/<USER>/anaconda3/envs/htc/include -fPIC -O2 -isystem /home/<USER>/anaconda3/envs/htc/include -fPIC -c /tmp/tmp4mr7q8kv/test.c -o /tmp/tmp4mr7q8kv/test.o
2025-09-01 17:18:47,744 - root - INFO - gcc -pthread -B /home/<USER>/anaconda3/envs/htc/compiler_compat /tmp/tmp4mr7q8kv/test.o -L/usr/local/cuda -L/usr/local/cuda/lib64 -lcufile -o /tmp/tmp4mr7q8kv/a.out
2025-09-01 17:18:47,815 - root - INFO - gcc -pthread -B /home/<USER>/anaconda3/envs/htc/compiler_compat -fno-strict-overflow -Wsign-compare -DNDEBUG -O2 -Wall -fPIC -O2 -isystem /home/<USER>/anaconda3/envs/htc/include -fPIC -O2 -isystem /home/<USER>/anaconda3/envs/htc/include -fPIC -c /tmp/tmp_p9469v_/test.c -o /tmp/tmp_p9469v_/test.o
2025-09-01 17:18:47,833 - root - INFO - gcc -pthread -B /home/<USER>/anaconda3/envs/htc/compiler_compat /tmp/tmp_p9469v_/test.o -laio -o /tmp/tmp_p9469v_/a.out
2025-09-01 17:18:52,028 - __main__ - ERROR - 运行出错: Attempting to unscale FP16 gradients.
2025-09-01 17:19:41,786 - __main__ - INFO - 检查运行环境...
2025-09-01 17:19:47,815 - __main__ - INFO - 环境验证通过
2025-09-01 17:19:47,815 - __main__ - INFO - 开始训练分类模型...
2025-09-01 17:19:48,533 - __main__ - INFO - 训练数据: 48211 个样本
2025-09-01 17:19:48,533 - __main__ - INFO - 验证数据: 5073 个样本
2025-09-01 17:19:48,533 - __main__ - INFO - 唯一标签数量: 2302
2025-09-01 17:19:56,779 - root - INFO - gcc -pthread -B /home/<USER>/anaconda3/envs/htc/compiler_compat -fno-strict-overflow -Wsign-compare -DNDEBUG -O2 -Wall -fPIC -O2 -isystem /home/<USER>/anaconda3/envs/htc/include -fPIC -O2 -isystem /home/<USER>/anaconda3/envs/htc/include -fPIC -c /tmp/tmp0g2zemag/test.c -o /tmp/tmp0g2zemag/test.o
2025-09-01 17:19:56,801 - root - INFO - gcc -pthread -B /home/<USER>/anaconda3/envs/htc/compiler_compat /tmp/tmp0g2zemag/test.o -laio -o /tmp/tmp0g2zemag/a.out
2025-09-01 17:19:57,328 - root - INFO - gcc -pthread -B /home/<USER>/anaconda3/envs/htc/compiler_compat -fno-strict-overflow -Wsign-compare -DNDEBUG -O2 -Wall -fPIC -O2 -isystem /home/<USER>/anaconda3/envs/htc/include -fPIC -O2 -isystem /home/<USER>/anaconda3/envs/htc/include -fPIC -c /tmp/tmpxo5dhylu/test.c -o /tmp/tmpxo5dhylu/test.o
2025-09-01 17:19:57,350 - root - INFO - gcc -pthread -B /home/<USER>/anaconda3/envs/htc/compiler_compat /tmp/tmpxo5dhylu/test.o -L/usr/local/cuda -L/usr/local/cuda/lib64 -lcufile -o /tmp/tmpxo5dhylu/a.out
2025-09-01 17:19:57,412 - root - INFO - gcc -pthread -B /home/<USER>/anaconda3/envs/htc/compiler_compat -fno-strict-overflow -Wsign-compare -DNDEBUG -O2 -Wall -fPIC -O2 -isystem /home/<USER>/anaconda3/envs/htc/include -fPIC -O2 -isystem /home/<USER>/anaconda3/envs/htc/include -fPIC -c /tmp/tmpbnjfrwru/test.c -o /tmp/tmpbnjfrwru/test.o
2025-09-01 17:19:57,433 - root - INFO - gcc -pthread -B /home/<USER>/anaconda3/envs/htc/compiler_compat /tmp/tmpbnjfrwru/test.o -laio -o /tmp/tmpbnjfrwru/a.out
2025-09-01 17:22:55,880 - __main__ - INFO - 检查运行环境...
2025-09-01 17:23:00,857 - __main__ - INFO - 环境验证通过
2025-09-01 17:23:00,857 - __main__ - INFO - 开始训练分类模型...
2025-09-01 17:23:01,520 - __main__ - INFO - 训练数据: 48211 个样本
2025-09-01 17:23:01,520 - __main__ - INFO - 验证数据: 5073 个样本
2025-09-01 17:23:01,520 - __main__ - INFO - 唯一标签数量: 2302
2025-09-01 17:23:08,733 - root - INFO - gcc -pthread -B /home/<USER>/anaconda3/envs/htc/compiler_compat -fno-strict-overflow -Wsign-compare -DNDEBUG -O2 -Wall -fPIC -O2 -isystem /home/<USER>/anaconda3/envs/htc/include -fPIC -O2 -isystem /home/<USER>/anaconda3/envs/htc/include -fPIC -c /tmp/tmpsxqg8up3/test.c -o /tmp/tmpsxqg8up3/test.o
2025-09-01 17:23:08,751 - root - INFO - gcc -pthread -B /home/<USER>/anaconda3/envs/htc/compiler_compat /tmp/tmpsxqg8up3/test.o -laio -o /tmp/tmpsxqg8up3/a.out
2025-09-01 17:23:09,204 - root - INFO - gcc -pthread -B /home/<USER>/anaconda3/envs/htc/compiler_compat -fno-strict-overflow -Wsign-compare -DNDEBUG -O2 -Wall -fPIC -O2 -isystem /home/<USER>/anaconda3/envs/htc/include -fPIC -O2 -isystem /home/<USER>/anaconda3/envs/htc/include -fPIC -c /tmp/tmpev6u2d6q/test.c -o /tmp/tmpev6u2d6q/test.o
2025-09-01 17:23:09,223 - root - INFO - gcc -pthread -B /home/<USER>/anaconda3/envs/htc/compiler_compat /tmp/tmpev6u2d6q/test.o -L/usr/local/cuda -L/usr/local/cuda/lib64 -lcufile -o /tmp/tmpev6u2d6q/a.out
2025-09-01 17:23:09,287 - root - INFO - gcc -pthread -B /home/<USER>/anaconda3/envs/htc/compiler_compat -fno-strict-overflow -Wsign-compare -DNDEBUG -O2 -Wall -fPIC -O2 -isystem /home/<USER>/anaconda3/envs/htc/include -fPIC -O2 -isystem /home/<USER>/anaconda3/envs/htc/include -fPIC -c /tmp/tmp99i88ak8/test.c -o /tmp/tmp99i88ak8/test.o
2025-09-01 17:23:09,307 - root - INFO - gcc -pthread -B /home/<USER>/anaconda3/envs/htc/compiler_compat /tmp/tmp99i88ak8/test.o -laio -o /tmp/tmp99i88ak8/a.out
2025-09-01 17:31:55,571 - __main__ - INFO - 检查运行环境...
2025-09-01 17:32:00,393 - __main__ - INFO - 环境验证通过
2025-09-01 17:32:00,393 - __main__ - INFO - 开始训练分类模型...
2025-09-01 17:32:00,394 - faiss.loader - INFO - Loading faiss with AVX512 support.
2025-09-01 17:32:00,414 - faiss.loader - INFO - Successfully loaded faiss with AVX512 support.
2025-09-01 17:32:01,434 - embedding_model - INFO - 从缓存加载嵌入向量: /home/<USER>/ZhouSQ/DCX/TACL_math1/qwen-fintune/similarity_matching_project/outputs/embeddings_cache/train_embeddings.pkl
2025-09-01 17:32:10,154 - root - INFO - gcc -pthread -B /home/<USER>/anaconda3/envs/htc/compiler_compat -fno-strict-overflow -Wsign-compare -DNDEBUG -O2 -Wall -fPIC -O2 -isystem /home/<USER>/anaconda3/envs/htc/include -fPIC -O2 -isystem /home/<USER>/anaconda3/envs/htc/include -fPIC -c /tmp/tmpereky9c9/test.c -o /tmp/tmpereky9c9/test.o
2025-09-01 17:32:10,174 - root - INFO - gcc -pthread -B /home/<USER>/anaconda3/envs/htc/compiler_compat /tmp/tmpereky9c9/test.o -laio -o /tmp/tmpereky9c9/a.out
2025-09-01 17:32:10,709 - root - INFO - gcc -pthread -B /home/<USER>/anaconda3/envs/htc/compiler_compat -fno-strict-overflow -Wsign-compare -DNDEBUG -O2 -Wall -fPIC -O2 -isystem /home/<USER>/anaconda3/envs/htc/include -fPIC -O2 -isystem /home/<USER>/anaconda3/envs/htc/include -fPIC -c /tmp/tmp7iupto3a/test.c -o /tmp/tmp7iupto3a/test.o
2025-09-01 17:32:10,729 - root - INFO - gcc -pthread -B /home/<USER>/anaconda3/envs/htc/compiler_compat /tmp/tmp7iupto3a/test.o -L/usr/local/cuda -L/usr/local/cuda/lib64 -lcufile -o /tmp/tmp7iupto3a/a.out
2025-09-01 17:32:10,789 - root - INFO - gcc -pthread -B /home/<USER>/anaconda3/envs/htc/compiler_compat -fno-strict-overflow -Wsign-compare -DNDEBUG -O2 -Wall -fPIC -O2 -isystem /home/<USER>/anaconda3/envs/htc/include -fPIC -O2 -isystem /home/<USER>/anaconda3/envs/htc/include -fPIC -c /tmp/tmpelk49tyh/test.c -o /tmp/tmpelk49tyh/test.o
2025-09-01 17:32:10,808 - root - INFO - gcc -pthread -B /home/<USER>/anaconda3/envs/htc/compiler_compat /tmp/tmpelk49tyh/test.o -laio -o /tmp/tmpelk49tyh/a.out
2025-09-01 17:32:13,644 - __main__ - ERROR - 运行出错: CUDA out of memory. Tried to allocate 608.00 MiB. GPU 1 has a total capacity of 39.50 GiB of which 117.88 MiB is free. Including non-PyTorch memory, this process has 39.36 GiB memory in use. Of the allocated memory 38.21 GiB is allocated by PyTorch, and 671.87 MiB is reserved by PyTorch but unallocated. If reserved but unallocated memory is large try setting PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True to avoid fragmentation.  See documentation for Memory Management  (https://pytorch.org/docs/stable/notes/cuda.html#environment-variables)
2025-09-01 17:32:32,932 - __main__ - INFO - 检查运行环境...
2025-09-01 17:32:37,862 - __main__ - INFO - 环境验证通过
2025-09-01 17:32:37,862 - __main__ - INFO - 开始训练分类模型...
2025-09-01 17:32:37,863 - faiss.loader - INFO - Loading faiss with AVX512 support.
2025-09-01 17:32:37,881 - faiss.loader - INFO - Successfully loaded faiss with AVX512 support.
2025-09-01 17:32:38,862 - embedding_model - INFO - 从缓存加载嵌入向量: /home/<USER>/ZhouSQ/DCX/TACL_math1/qwen-fintune/similarity_matching_project/outputs/embeddings_cache/train_embeddings.pkl
2025-09-01 17:32:47,606 - root - INFO - gcc -pthread -B /home/<USER>/anaconda3/envs/htc/compiler_compat -fno-strict-overflow -Wsign-compare -DNDEBUG -O2 -Wall -fPIC -O2 -isystem /home/<USER>/anaconda3/envs/htc/include -fPIC -O2 -isystem /home/<USER>/anaconda3/envs/htc/include -fPIC -c /tmp/tmppjzrbm_3/test.c -o /tmp/tmppjzrbm_3/test.o
2025-09-01 17:32:47,626 - root - INFO - gcc -pthread -B /home/<USER>/anaconda3/envs/htc/compiler_compat /tmp/tmppjzrbm_3/test.o -laio -o /tmp/tmppjzrbm_3/a.out
2025-09-01 17:32:48,168 - root - INFO - gcc -pthread -B /home/<USER>/anaconda3/envs/htc/compiler_compat -fno-strict-overflow -Wsign-compare -DNDEBUG -O2 -Wall -fPIC -O2 -isystem /home/<USER>/anaconda3/envs/htc/include -fPIC -O2 -isystem /home/<USER>/anaconda3/envs/htc/include -fPIC -c /tmp/tmplzvq7iyy/test.c -o /tmp/tmplzvq7iyy/test.o
2025-09-01 17:32:48,185 - root - INFO - gcc -pthread -B /home/<USER>/anaconda3/envs/htc/compiler_compat /tmp/tmplzvq7iyy/test.o -L/usr/local/cuda -L/usr/local/cuda/lib64 -lcufile -o /tmp/tmplzvq7iyy/a.out
2025-09-01 17:32:48,255 - root - INFO - gcc -pthread -B /home/<USER>/anaconda3/envs/htc/compiler_compat -fno-strict-overflow -Wsign-compare -DNDEBUG -O2 -Wall -fPIC -O2 -isystem /home/<USER>/anaconda3/envs/htc/include -fPIC -O2 -isystem /home/<USER>/anaconda3/envs/htc/include -fPIC -c /tmp/tmp7506cey2/test.c -o /tmp/tmp7506cey2/test.o
2025-09-01 17:32:48,277 - root - INFO - gcc -pthread -B /home/<USER>/anaconda3/envs/htc/compiler_compat /tmp/tmp7506cey2/test.o -laio -o /tmp/tmp7506cey2/a.out
2025-09-02 16:31:31,634 - __main__ - INFO - 检查运行环境...
2025-09-02 16:31:36,137 - __main__ - INFO - 环境验证通过
2025-09-02 16:31:36,137 - __main__ - INFO - 开始训练分类模型...
2025-09-02 16:31:36,138 - faiss.loader - INFO - Loading faiss with AVX512 support.
2025-09-02 16:31:36,155 - faiss.loader - INFO - Successfully loaded faiss with AVX512 support.
2025-09-02 16:31:37,131 - embedding_model - INFO - 从缓存加载嵌入向量: /home/<USER>/ZhouSQ/DCX/TACL_math1/qwen-fintune/similarity_matching_project/outputs/embeddings_cache/train_embeddings.pkl
2025-09-02 16:31:44,555 - __main__ - ERROR - 运行出错: TrainingArguments.__init__() got an unexpected keyword argument 'save_epochs'
2025-09-02 16:36:33,892 - __main__ - INFO - 检查运行环境...
2025-09-02 16:36:38,706 - __main__ - INFO - 环境验证通过
2025-09-02 16:36:38,706 - __main__ - INFO - 开始训练分类模型...
2025-09-02 16:36:38,707 - faiss.loader - INFO - Loading faiss with AVX512 support.
2025-09-02 16:36:38,726 - faiss.loader - INFO - Successfully loaded faiss with AVX512 support.
2025-09-02 16:36:39,717 - embedding_model - INFO - 从缓存加载嵌入向量: /home/<USER>/ZhouSQ/DCX/TACL_math1/qwen-fintune/similarity_matching_project/outputs/embeddings_cache/train_embeddings.pkl
2025-09-02 16:36:47,303 - root - INFO - gcc -pthread -B /home/<USER>/anaconda3/envs/htc/compiler_compat -fno-strict-overflow -Wsign-compare -DNDEBUG -O2 -Wall -fPIC -O2 -isystem /home/<USER>/anaconda3/envs/htc/include -fPIC -O2 -isystem /home/<USER>/anaconda3/envs/htc/include -fPIC -c /tmp/tmpelamv6po/test.c -o /tmp/tmpelamv6po/test.o
2025-09-02 16:36:47,321 - root - INFO - gcc -pthread -B /home/<USER>/anaconda3/envs/htc/compiler_compat /tmp/tmpelamv6po/test.o -laio -o /tmp/tmpelamv6po/a.out
2025-09-02 16:36:47,856 - root - INFO - gcc -pthread -B /home/<USER>/anaconda3/envs/htc/compiler_compat -fno-strict-overflow -Wsign-compare -DNDEBUG -O2 -Wall -fPIC -O2 -isystem /home/<USER>/anaconda3/envs/htc/include -fPIC -O2 -isystem /home/<USER>/anaconda3/envs/htc/include -fPIC -c /tmp/tmpn13zj0qr/test.c -o /tmp/tmpn13zj0qr/test.o
2025-09-02 16:36:47,874 - root - INFO - gcc -pthread -B /home/<USER>/anaconda3/envs/htc/compiler_compat /tmp/tmpn13zj0qr/test.o -L/usr/local/cuda -L/usr/local/cuda/lib64 -lcufile -o /tmp/tmpn13zj0qr/a.out
2025-09-02 16:36:47,930 - root - INFO - gcc -pthread -B /home/<USER>/anaconda3/envs/htc/compiler_compat -fno-strict-overflow -Wsign-compare -DNDEBUG -O2 -Wall -fPIC -O2 -isystem /home/<USER>/anaconda3/envs/htc/include -fPIC -O2 -isystem /home/<USER>/anaconda3/envs/htc/include -fPIC -c /tmp/tmppglbsk1q/test.c -o /tmp/tmppglbsk1q/test.o
2025-09-02 16:36:47,947 - root - INFO - gcc -pthread -B /home/<USER>/anaconda3/envs/htc/compiler_compat /tmp/tmppglbsk1q/test.o -laio -o /tmp/tmppglbsk1q/a.out
2025-09-02 16:36:49,917 - __main__ - ERROR - 运行出错: CUDA out of memory. Tried to allocate 128.00 MiB. GPU 1 has a total capacity of 39.50 GiB of which 46.31 MiB is free. Process 1803571 has 14.60 GiB memory in use. Including non-PyTorch memory, this process has 24.82 GiB memory in use. Of the allocated memory 23.96 GiB is allocated by PyTorch, and 379.09 MiB is reserved by PyTorch but unallocated. If reserved but unallocated memory is large try setting PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True to avoid fragmentation.  See documentation for Memory Management  (https://pytorch.org/docs/stable/notes/cuda.html#environment-variables)
2025-09-02 16:37:40,561 - __main__ - INFO - 检查运行环境...
2025-09-02 16:37:45,117 - __main__ - INFO - 环境验证通过
2025-09-02 16:37:45,117 - __main__ - INFO - 开始训练分类模型...
2025-09-02 16:37:45,118 - faiss.loader - INFO - Loading faiss with AVX512 support.
2025-09-02 16:37:45,135 - faiss.loader - INFO - Successfully loaded faiss with AVX512 support.
2025-09-02 16:37:46,122 - embedding_model - INFO - 从缓存加载嵌入向量: /home/<USER>/ZhouSQ/DCX/TACL_math1/qwen-fintune/similarity_matching_project/outputs/embeddings_cache/train_embeddings.pkl
2025-09-02 16:37:53,775 - root - INFO - gcc -pthread -B /home/<USER>/anaconda3/envs/htc/compiler_compat -fno-strict-overflow -Wsign-compare -DNDEBUG -O2 -Wall -fPIC -O2 -isystem /home/<USER>/anaconda3/envs/htc/include -fPIC -O2 -isystem /home/<USER>/anaconda3/envs/htc/include -fPIC -c /tmp/tmp7d180q0j/test.c -o /tmp/tmp7d180q0j/test.o
2025-09-02 16:37:53,793 - root - INFO - gcc -pthread -B /home/<USER>/anaconda3/envs/htc/compiler_compat /tmp/tmp7d180q0j/test.o -laio -o /tmp/tmp7d180q0j/a.out
2025-09-02 16:37:54,309 - root - INFO - gcc -pthread -B /home/<USER>/anaconda3/envs/htc/compiler_compat -fno-strict-overflow -Wsign-compare -DNDEBUG -O2 -Wall -fPIC -O2 -isystem /home/<USER>/anaconda3/envs/htc/include -fPIC -O2 -isystem /home/<USER>/anaconda3/envs/htc/include -fPIC -c /tmp/tmp8bbbwwig/test.c -o /tmp/tmp8bbbwwig/test.o
2025-09-02 16:37:54,329 - root - INFO - gcc -pthread -B /home/<USER>/anaconda3/envs/htc/compiler_compat /tmp/tmp8bbbwwig/test.o -L/usr/local/cuda -L/usr/local/cuda/lib64 -lcufile -o /tmp/tmp8bbbwwig/a.out
2025-09-02 16:37:54,394 - root - INFO - gcc -pthread -B /home/<USER>/anaconda3/envs/htc/compiler_compat -fno-strict-overflow -Wsign-compare -DNDEBUG -O2 -Wall -fPIC -O2 -isystem /home/<USER>/anaconda3/envs/htc/include -fPIC -O2 -isystem /home/<USER>/anaconda3/envs/htc/include -fPIC -c /tmp/tmpqh0q6hux/test.c -o /tmp/tmpqh0q6hux/test.o
2025-09-02 16:37:54,413 - root - INFO - gcc -pthread -B /home/<USER>/anaconda3/envs/htc/compiler_compat /tmp/tmpqh0q6hux/test.o -laio -o /tmp/tmpqh0q6hux/a.out
2025-09-03 11:32:21,414 - __main__ - INFO - 检查运行环境...
2025-09-03 11:32:26,281 - __main__ - INFO - 环境验证通过
2025-09-03 11:32:26,281 - __main__ - INFO - 开始模型评估，策略: auto
2025-09-03 11:32:26,282 - faiss.loader - INFO - Loading faiss with AVX512 support.
2025-09-03 11:32:26,302 - faiss.loader - INFO - Successfully loaded faiss with AVX512 support.
2025-09-03 11:32:27,297 - embedding_model - INFO - 从缓存加载嵌入向量: /home/<USER>/ZhouSQ/DCX/TACL_math1/qwen-fintune/similarity_matching_project/outputs/embeddings_cache/train_embeddings.pkl
2025-09-03 11:32:35,047 - root - INFO - gcc -pthread -B /home/<USER>/anaconda3/envs/htc/compiler_compat -fno-strict-overflow -Wsign-compare -DNDEBUG -O2 -Wall -fPIC -O2 -isystem /home/<USER>/anaconda3/envs/htc/include -fPIC -O2 -isystem /home/<USER>/anaconda3/envs/htc/include -fPIC -c /tmp/tmp4vh7mb3p/test.c -o /tmp/tmp4vh7mb3p/test.o
2025-09-03 11:32:35,068 - root - INFO - gcc -pthread -B /home/<USER>/anaconda3/envs/htc/compiler_compat /tmp/tmp4vh7mb3p/test.o -laio -o /tmp/tmp4vh7mb3p/a.out
2025-09-03 11:32:35,563 - root - INFO - gcc -pthread -B /home/<USER>/anaconda3/envs/htc/compiler_compat -fno-strict-overflow -Wsign-compare -DNDEBUG -O2 -Wall -fPIC -O2 -isystem /home/<USER>/anaconda3/envs/htc/include -fPIC -O2 -isystem /home/<USER>/anaconda3/envs/htc/include -fPIC -c /tmp/tmp25gl_hqc/test.c -o /tmp/tmp25gl_hqc/test.o
2025-09-03 11:32:35,583 - root - INFO - gcc -pthread -B /home/<USER>/anaconda3/envs/htc/compiler_compat /tmp/tmp25gl_hqc/test.o -L/usr/local/cuda -L/usr/local/cuda/lib64 -lcufile -o /tmp/tmp25gl_hqc/a.out
2025-09-03 11:32:35,653 - root - INFO - gcc -pthread -B /home/<USER>/anaconda3/envs/htc/compiler_compat -fno-strict-overflow -Wsign-compare -DNDEBUG -O2 -Wall -fPIC -O2 -isystem /home/<USER>/anaconda3/envs/htc/include -fPIC -O2 -isystem /home/<USER>/anaconda3/envs/htc/include -fPIC -c /tmp/tmp98mnucw1/test.c -o /tmp/tmp98mnucw1/test.o
2025-09-03 11:32:35,672 - root - INFO - gcc -pthread -B /home/<USER>/anaconda3/envs/htc/compiler_compat /tmp/tmp98mnucw1/test.o -laio -o /tmp/tmp98mnucw1/a.out
2025-09-03 11:33:17,321 - __main__ - INFO - 检查运行环境...
2025-09-03 11:33:22,161 - __main__ - INFO - 环境验证通过
2025-09-03 11:33:22,161 - __main__ - INFO - 开始模型评估，策略: auto
2025-09-03 11:33:22,162 - faiss.loader - INFO - Loading faiss with AVX512 support.
2025-09-03 11:33:22,182 - faiss.loader - INFO - Successfully loaded faiss with AVX512 support.
2025-09-03 11:33:23,168 - embedding_model - INFO - 从缓存加载嵌入向量: /home/<USER>/ZhouSQ/DCX/TACL_math1/qwen-fintune/similarity_matching_project/outputs/embeddings_cache/train_embeddings.pkl
2025-09-03 11:33:30,699 - root - INFO - gcc -pthread -B /home/<USER>/anaconda3/envs/htc/compiler_compat -fno-strict-overflow -Wsign-compare -DNDEBUG -O2 -Wall -fPIC -O2 -isystem /home/<USER>/anaconda3/envs/htc/include -fPIC -O2 -isystem /home/<USER>/anaconda3/envs/htc/include -fPIC -c /tmp/tmpfcncooxs/test.c -o /tmp/tmpfcncooxs/test.o
2025-09-03 11:33:30,718 - root - INFO - gcc -pthread -B /home/<USER>/anaconda3/envs/htc/compiler_compat /tmp/tmpfcncooxs/test.o -laio -o /tmp/tmpfcncooxs/a.out
2025-09-03 11:33:31,212 - root - INFO - gcc -pthread -B /home/<USER>/anaconda3/envs/htc/compiler_compat -fno-strict-overflow -Wsign-compare -DNDEBUG -O2 -Wall -fPIC -O2 -isystem /home/<USER>/anaconda3/envs/htc/include -fPIC -O2 -isystem /home/<USER>/anaconda3/envs/htc/include -fPIC -c /tmp/tmpjn1sc4tq/test.c -o /tmp/tmpjn1sc4tq/test.o
2025-09-03 11:33:31,231 - root - INFO - gcc -pthread -B /home/<USER>/anaconda3/envs/htc/compiler_compat /tmp/tmpjn1sc4tq/test.o -L/usr/local/cuda -L/usr/local/cuda/lib64 -lcufile -o /tmp/tmpjn1sc4tq/a.out
2025-09-03 11:33:31,300 - root - INFO - gcc -pthread -B /home/<USER>/anaconda3/envs/htc/compiler_compat -fno-strict-overflow -Wsign-compare -DNDEBUG -O2 -Wall -fPIC -O2 -isystem /home/<USER>/anaconda3/envs/htc/include -fPIC -O2 -isystem /home/<USER>/anaconda3/envs/htc/include -fPIC -c /tmp/tmpovzypkgd/test.c -o /tmp/tmpovzypkgd/test.o
2025-09-03 11:33:31,319 - root - INFO - gcc -pthread -B /home/<USER>/anaconda3/envs/htc/compiler_compat /tmp/tmpovzypkgd/test.o -laio -o /tmp/tmpovzypkgd/a.out
2025-09-03 11:50:37,326 - __main__ - INFO - 检查运行环境...
2025-09-03 11:50:41,768 - __main__ - INFO - 环境验证通过
2025-09-03 11:50:41,768 - __main__ - INFO - 开始模型评估，策略: auto
2025-09-03 11:50:41,769 - faiss.loader - INFO - Loading faiss with AVX512 support.
2025-09-03 11:50:41,786 - faiss.loader - INFO - Successfully loaded faiss with AVX512 support.
2025-09-03 11:50:42,773 - embedding_model - INFO - 从缓存加载嵌入向量: /home/<USER>/ZhouSQ/DCX/TACL_math1/qwen-fintune/similarity_matching_project/outputs/embeddings_cache/train_embeddings.pkl
2025-09-03 11:50:50,357 - root - INFO - gcc -pthread -B /home/<USER>/anaconda3/envs/htc/compiler_compat -fno-strict-overflow -Wsign-compare -DNDEBUG -O2 -Wall -fPIC -O2 -isystem /home/<USER>/anaconda3/envs/htc/include -fPIC -O2 -isystem /home/<USER>/anaconda3/envs/htc/include -fPIC -c /tmp/tmpulalgose/test.c -o /tmp/tmpulalgose/test.o
2025-09-03 11:50:50,377 - root - INFO - gcc -pthread -B /home/<USER>/anaconda3/envs/htc/compiler_compat /tmp/tmpulalgose/test.o -laio -o /tmp/tmpulalgose/a.out
2025-09-03 11:50:50,871 - root - INFO - gcc -pthread -B /home/<USER>/anaconda3/envs/htc/compiler_compat -fno-strict-overflow -Wsign-compare -DNDEBUG -O2 -Wall -fPIC -O2 -isystem /home/<USER>/anaconda3/envs/htc/include -fPIC -O2 -isystem /home/<USER>/anaconda3/envs/htc/include -fPIC -c /tmp/tmpjr_cwkm8/test.c -o /tmp/tmpjr_cwkm8/test.o
2025-09-03 11:50:50,894 - root - INFO - gcc -pthread -B /home/<USER>/anaconda3/envs/htc/compiler_compat /tmp/tmpjr_cwkm8/test.o -L/usr/local/cuda -L/usr/local/cuda/lib64 -lcufile -o /tmp/tmpjr_cwkm8/a.out
2025-09-03 11:50:50,959 - root - INFO - gcc -pthread -B /home/<USER>/anaconda3/envs/htc/compiler_compat -fno-strict-overflow -Wsign-compare -DNDEBUG -O2 -Wall -fPIC -O2 -isystem /home/<USER>/anaconda3/envs/htc/include -fPIC -O2 -isystem /home/<USER>/anaconda3/envs/htc/include -fPIC -c /tmp/tmplw20qkbp/test.c -o /tmp/tmplw20qkbp/test.o
2025-09-03 11:50:50,976 - root - INFO - gcc -pthread -B /home/<USER>/anaconda3/envs/htc/compiler_compat /tmp/tmplw20qkbp/test.o -laio -o /tmp/tmplw20qkbp/a.out
2025-09-03 11:50:53,013 - __main__ - ERROR - 运行出错: CUDA out of memory. Tried to allocate 152.00 MiB. GPU 1 has a total capacity of 39.50 GiB of which 6.31 MiB is free. Process 1922683 has 29.60 GiB memory in use. Including non-PyTorch memory, this process has 9.86 GiB memory in use. Of the allocated memory 9.22 GiB is allocated by PyTorch, and 154.15 MiB is reserved by PyTorch but unallocated. If reserved but unallocated memory is large try setting PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True to avoid fragmentation.  See documentation for Memory Management  (https://pytorch.org/docs/stable/notes/cuda.html#environment-variables)
