#!/usr/bin/env python3
"""
数学分类系统 - 主程序入口
简单易用的命令行界面

使用方法:
    python main.py build                    # 构建向量知识库
    python main.py train                    # 训练LoRA微调模型
    python main.py evaluate                 # 评估系统性能
    python main.py predict "你的问题"        # 单次预测
    python main.py interactive              # 交互式预测
    python main.py status                   # 查看系统状态
"""

import argparse
import sys
import logging
from pathlib import Path

# 添加项目路径
sys.path.insert(0, str(Path(__file__).parent))

from math_classification_system import MathClassificationSystem, Config

def setup_logging(verbose: bool = False):
    """设置日志"""
    level = logging.DEBUG if verbose else logging.INFO
    logging.basicConfig(
        level=level,
        format='%(asctime)s - %(levelname)s - %(message)s',
        datefmt='%H:%M:%S'
    )

def print_banner():
    """打印系统横幅"""
    print("=" * 60)
    print("🧮 数学分类系统 - 基于阈值的智能分类")
    print("=" * 60)
    print(f"📊 相似度阈值: {Config.SIMILARITY_THRESHOLD}")
    print(f"🔧 设备: {Config.DEVICE}")
    print(f"⚡ LoRA微调: {'启用' if Config.USE_LORA else '禁用'}")
    print("=" * 60)

def cmd_build(args):
    """构建向量知识库"""
    print("🔨 构建向量知识库...")
    
    system = MathClassificationSystem()
    success = system.build_vectors(
        use_cache=not args.no_cache,
        force_rebuild=args.force
    )
    
    if success:
        print("✅ 向量知识库构建成功!")
    else:
        print("❌ 向量知识库构建失败!")
        sys.exit(1)

def cmd_train(args):
    """训练LoRA微调模型"""
    print("🚀 训练LoRA微调模型...")
    
    system = MathClassificationSystem()
    success = system.train_model(force_retrain=args.force)
    
    if success:
        print("✅ 模型训练成功!")
    else:
        print("❌ 模型训练失败!")
        sys.exit(1)

def cmd_evaluate(args):
    """评估系统性能"""
    print("📊 评估系统性能...")
    
    system = MathClassificationSystem()
    results = system.evaluate_system()
    
    if 'error' in results:
        print(f"❌ 评估失败: {results['error']}")
        sys.exit(1)
    
    # 显示评估结果
    print("\n📈 评估结果:")
    print("-" * 40)
    
    for method_name, method_results in results.items():
        if isinstance(method_results, dict) and 'accuracy' in method_results:
            accuracy = method_results['accuracy']
            correct = method_results['correct']
            total = method_results['total']
            
            method_display = {
                'vector_retrieval': '向量检索',
                'model_classification': '模型分类',
                'hybrid_system': '混合系统'
            }.get(method_name, method_name)
            
            print(f"{method_display:12}: {accuracy:.3f} ({correct}/{total})")
            
            if method_name == 'hybrid_system' and 'strategy_usage' in method_results:
                usage = method_results['strategy_usage']
                print(f"  策略使用: 向量检索 {usage.get('vector_retrieval', 0)} 次, "
                      f"模型分类 {usage.get('model_classification', 0)} 次")
    
    print("✅ 评估完成!")

def cmd_predict(args):
    """单次预测"""
    if not args.query:
        print("❌ 请提供查询文本: --query '你的问题'")
        sys.exit(1)
    
    print(f"🔍 预测查询: {args.query}")
    
    system = MathClassificationSystem()
    result = system.predict(args.query, return_details=args.verbose)
    
    if 'error' in result:
        print(f"❌ 预测失败: {result['error']}")
        sys.exit(1)
    
    # 显示预测结果
    print("\n🎯 预测结果:")
    print("-" * 40)
    print(f"预测标签: {' -> '.join(result['prediction']) if result['prediction'] else '无'}")
    print(f"置信度: {result['confidence']:.3f}")
    print(f"使用策略: {result['strategy_used']}")
    print(f"推理过程: {result['reasoning']}")
    
    if args.verbose and 'details' in result:
        print("\n📋 详细信息:")
        for key, value in result['details'].items():
            print(f"  {key}: {value}")

def cmd_interactive(args):
    """交互式预测"""
    print("🎮 交互式预测模式")
    print("输入查询进行预测，输入 'quit' 退出，输入 'help' 查看帮助")
    print("-" * 40)
    
    system = MathClassificationSystem()
    
    # 预加载系统
    print("正在初始化系统...")
    if not system.build_vectors():
        print("❌ 向量知识库初始化失败")
        return
    if not system.train_model():
        print("❌ 模型初始化失败")
        return
    print("✅ 系统初始化完成\n")
    
    while True:
        try:
            query = input("🔍 请输入查询 (quit退出): ").strip()
            
            if query.lower() == 'quit':
                print("👋 再见!")
                break
            elif query.lower() == 'help':
                print("\n💡 帮助信息:")
                print("  • 输入任何数学问题进行分类预测")
                print("  • 输入 'quit' 退出程序")
                print("  • 输入 'explain <问题>' 查看详细预测过程")
                print(f"  • 当前阈值: {Config.SIMILARITY_THRESHOLD}")
                print()
                continue
            elif query.startswith('explain '):
                explain_query = query[8:].strip()
                if explain_query:
                    explanation = system.predictor.explain_prediction(
                        explain_query, system.vector_builder, system.model_trainer
                    )
                    print(f"\n🔍 查询: {explanation['query']}")
                    print(f"🎯 最终预测: {' -> '.join(explanation.get('final_prediction', [])) if explanation.get('final_prediction') else '无'}")
                    print(f"📊 置信度: {explanation.get('confidence', 0):.3f}")
                    print(f"🔧 决策: {explanation.get('decision', '未知')}")
                    print("\n📋 预测过程:")
                    for step in explanation.get('process', []):
                        print(f"  • {step}")
                    print()
                continue
            elif not query:
                continue
            
            # 执行预测
            result = system.predict(query)
            
            if 'error' in result:
                print(f"❌ 预测失败: {result['error']}")
                continue
            
            print(f"\n🎯 预测结果: {' -> '.join(result['prediction']) if result['prediction'] else '无'}")
            print(f"📊 置信度: {result['confidence']:.3f}")
            print(f"🔧 策略: {result['strategy_used']}")
            print(f"💭 推理: {result['reasoning']}")
            print()
            
        except KeyboardInterrupt:
            print("\n👋 程序被中断，再见!")
            break
        except Exception as e:
            print(f"❌ 出现错误: {e}")

def cmd_status(args):
    """查看系统状态"""
    print("📋 系统状态:")
    print("-" * 40)
    
    system = MathClassificationSystem()
    status = system.get_system_status()
    
    print(f"向量知识库: {'✅ 就绪' if status['vector_ready'] else '❌ 未就绪'}")
    print(f"分类模型: {'✅ 就绪' if status['model_ready'] else '❌ 未就绪'}")
    print(f"相似度阈值: {status['similarity_threshold']}")
    print(f"计算设备: {status['device']}")
    print(f"LoRA微调: {'✅ 启用' if status['use_lora'] else '❌ 禁用'}")
    
    # 检查路径
    print(f"\n📁 路径检查:")
    errors = Config.validate_paths()
    if errors:
        for error in errors:
            print(f"❌ {error}")
    else:
        print("✅ 所有路径正常")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="数学分类系统 - 基于阈值的智能分类",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  python main.py build                    # 构建向量知识库
  python main.py train                    # 训练LoRA微调模型
  python main.py evaluate                 # 评估系统性能
  python main.py predict --query "问题"   # 单次预测
  python main.py interactive              # 交互式预测
  python main.py status                   # 查看系统状态
        """
    )
    
    # 子命令
    subparsers = parser.add_subparsers(dest='command', help='可用命令')
    
    # build命令
    build_parser = subparsers.add_parser('build', help='构建向量知识库')
    build_parser.add_argument('--no-cache', action='store_true', help='不使用缓存')
    build_parser.add_argument('--force', action='store_true', help='强制重建')
    
    # train命令
    train_parser = subparsers.add_parser('train', help='训练LoRA微调模型')
    train_parser.add_argument('--force', action='store_true', help='强制重新训练')
    
    # evaluate命令
    eval_parser = subparsers.add_parser('evaluate', help='评估系统性能')
    
    # predict命令
    predict_parser = subparsers.add_parser('predict', help='单次预测')
    predict_parser.add_argument('--query', type=str, help='查询文本')
    predict_parser.add_argument('--verbose', action='store_true', help='显示详细信息')
    
    # interactive命令
    interactive_parser = subparsers.add_parser('interactive', help='交互式预测')
    
    # status命令
    status_parser = subparsers.add_parser('status', help='查看系统状态')
    
    # 全局参数
    parser.add_argument('--verbose', action='store_true', help='详细输出')
    
    args = parser.parse_args()
    
    # 设置日志
    setup_logging(args.verbose)
    
    # 显示横幅
    print_banner()
    
    # 执行命令
    if args.command == 'build':
        cmd_build(args)
    elif args.command == 'train':
        cmd_train(args)
    elif args.command == 'evaluate':
        cmd_evaluate(args)
    elif args.command == 'predict':
        cmd_predict(args)
    elif args.command == 'interactive':
        cmd_interactive(args)
    elif args.command == 'status':
        cmd_status(args)
    else:
        parser.print_help()

if __name__ == "__main__":
    main()
