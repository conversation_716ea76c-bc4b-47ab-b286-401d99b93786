{"best_global_step": 6027, "best_metric": 0.00019712201852946975, "best_model_checkpoint": "/home/<USER>/ZhouSQ/DCX/TACL_math1/qwen-fintune/similarity_matching_project/outputs/classification_model/checkpoint-6027", "epoch": 7.0, "eval_steps": 6026, "global_step": 42189, "is_hyper_param_search": false, "is_local_process_zero": true, "is_world_process_zero": true, "log_history": [{"epoch": 0.08296001327360213, "grad_norm": NaN, "learning_rate": 1.9960000000000002e-05, "loss": 5.5267, "step": 500}, {"epoch": 0.16592002654720425, "grad_norm": NaN, "learning_rate": 1.9833026601974236e-05, "loss": 0.0, "step": 1000}, {"epoch": 0.24888003982080636, "grad_norm": NaN, "learning_rate": 1.9665718587920364e-05, "loss": 0.0, "step": 1500}, {"epoch": 0.3318400530944085, "grad_norm": NaN, "learning_rate": 1.9498410573866492e-05, "loss": 0.0, "step": 2000}, {"epoch": 0.41480006636801064, "grad_norm": NaN, "learning_rate": 1.9331102559812617e-05, "loss": 0.0, "step": 2500}, {"epoch": 0.49776007964161273, "grad_norm": NaN, "learning_rate": 1.9163794545758742e-05, "loss": 0.0, "step": 3000}, {"epoch": 0.5807200929152149, "grad_norm": NaN, "learning_rate": 1.899648653170487e-05, "loss": 0.0, "step": 3500}, {"epoch": 0.663680106188817, "grad_norm": NaN, "learning_rate": 1.8829178517650998e-05, "loss": 0.0, "step": 4000}, {"epoch": 0.7466401194624191, "grad_norm": NaN, "learning_rate": 1.8661870503597123e-05, "loss": 0.0, "step": 4500}, {"epoch": 0.8296001327360213, "grad_norm": NaN, "learning_rate": 1.849456248954325e-05, "loss": 0.0, "step": 5000}, {"epoch": 0.9125601460096233, "grad_norm": NaN, "learning_rate": 1.832725447548938e-05, "loss": 0.0, "step": 5500}, {"epoch": 0.9955201592832255, "grad_norm": NaN, "learning_rate": 1.8159946461435504e-05, "loss": 0.0, "step": 6000}, {"epoch": 1.0, "eval_accuracy": 0.00019712201852946975, "eval_loss": NaN, "eval_runtime": 312.4269, "eval_samples_per_second": 16.237, "eval_steps_per_second": 1.018, "step": 6027}, {"epoch": 1.0784801725568276, "grad_norm": NaN, "learning_rate": 1.7992638447381632e-05, "loss": 0.0, "step": 6500}, {"epoch": 1.1614401858304297, "grad_norm": NaN, "learning_rate": 1.782533043332776e-05, "loss": 0.0, "step": 7000}, {"epoch": 1.2444001991040319, "grad_norm": NaN, "learning_rate": 1.7658022419273885e-05, "loss": 0.0, "step": 7500}, {"epoch": 1.327360212377634, "grad_norm": NaN, "learning_rate": 1.749071440522001e-05, "loss": 0.0, "step": 8000}, {"epoch": 1.4103202256512362, "grad_norm": NaN, "learning_rate": 1.7323406391166138e-05, "loss": 0.0, "step": 8500}, {"epoch": 1.4932802389248383, "grad_norm": NaN, "learning_rate": 1.7156098377112266e-05, "loss": 0.0, "step": 9000}, {"epoch": 1.5762402521984402, "grad_norm": NaN, "learning_rate": 1.698879036305839e-05, "loss": 0.0, "step": 9500}, {"epoch": 1.6592002654720424, "grad_norm": NaN, "learning_rate": 1.682148234900452e-05, "loss": 0.0, "step": 10000}, {"epoch": 1.7421602787456445, "grad_norm": NaN, "learning_rate": 1.6654174334950647e-05, "loss": 0.0, "step": 10500}, {"epoch": 1.8251202920192466, "grad_norm": NaN, "learning_rate": 1.6486866320896772e-05, "loss": 0.0, "step": 11000}, {"epoch": 1.9080803052928488, "grad_norm": NaN, "learning_rate": 1.63195583068429e-05, "loss": 0.0, "step": 11500}, {"epoch": 1.991040318566451, "grad_norm": NaN, "learning_rate": 1.6152250292789028e-05, "loss": 0.0, "step": 12000}, {"epoch": 2.0, "eval_accuracy": 0.00019712201852946975, "eval_loss": NaN, "eval_runtime": 294.7711, "eval_samples_per_second": 17.21, "eval_steps_per_second": 1.079, "step": 12054}, {"epoch": 2.074000331840053, "grad_norm": NaN, "learning_rate": 1.5984942278735153e-05, "loss": 0.0, "step": 12500}, {"epoch": 2.156960345113655, "grad_norm": NaN, "learning_rate": 1.5817634264681278e-05, "loss": 0.0, "step": 13000}, {"epoch": 2.2399203583872573, "grad_norm": NaN, "learning_rate": 1.5650326250627406e-05, "loss": 0.0, "step": 13500}, {"epoch": 2.3228803716608595, "grad_norm": NaN, "learning_rate": 1.5483018236573534e-05, "loss": 0.0, "step": 14000}, {"epoch": 2.4058403849344616, "grad_norm": NaN, "learning_rate": 1.531571022251966e-05, "loss": 0.0, "step": 14500}, {"epoch": 2.4888003982080638, "grad_norm": NaN, "learning_rate": 1.5148402208465787e-05, "loss": 0.0, "step": 15000}, {"epoch": 2.571760411481666, "grad_norm": NaN, "learning_rate": 1.4981094194411913e-05, "loss": 0.0, "step": 15500}, {"epoch": 2.654720424755268, "grad_norm": NaN, "learning_rate": 1.4813786180358041e-05, "loss": 0.0, "step": 16000}, {"epoch": 2.73768043802887, "grad_norm": NaN, "learning_rate": 1.4646478166304168e-05, "loss": 0.0, "step": 16500}, {"epoch": 2.8206404513024723, "grad_norm": NaN, "learning_rate": 1.4479170152250294e-05, "loss": 0.0, "step": 17000}, {"epoch": 2.9036004645760745, "grad_norm": NaN, "learning_rate": 1.4311862138196423e-05, "loss": 0.0, "step": 17500}, {"epoch": 2.9865604778496766, "grad_norm": NaN, "learning_rate": 1.4144554124142547e-05, "loss": 0.0, "step": 18000}, {"epoch": 3.0, "eval_accuracy": 0.00019712201852946975, "eval_loss": NaN, "eval_runtime": 308.3299, "eval_samples_per_second": 16.453, "eval_steps_per_second": 1.031, "step": 18081}, {"epoch": 3.0695204911232787, "grad_norm": NaN, "learning_rate": 1.3977246110088674e-05, "loss": 0.0, "step": 18500}, {"epoch": 3.152480504396881, "grad_norm": NaN, "learning_rate": 1.38099380960348e-05, "loss": 0.0, "step": 19000}, {"epoch": 3.235440517670483, "grad_norm": NaN, "learning_rate": 1.3642630081980928e-05, "loss": 0.0, "step": 19500}, {"epoch": 3.3184005309440847, "grad_norm": NaN, "learning_rate": 1.3475322067927055e-05, "loss": 0.0, "step": 20000}, {"epoch": 3.4013605442176873, "grad_norm": NaN, "learning_rate": 1.3308014053873181e-05, "loss": 0.0, "step": 20500}, {"epoch": 3.484320557491289, "grad_norm": NaN, "learning_rate": 1.314070603981931e-05, "loss": 0.0, "step": 21000}, {"epoch": 3.5672805707648916, "grad_norm": NaN, "learning_rate": 1.2973398025765436e-05, "loss": 0.0, "step": 21500}, {"epoch": 3.6502405840384933, "grad_norm": NaN, "learning_rate": 1.2806090011711562e-05, "loss": 0.0, "step": 22000}, {"epoch": 3.7332005973120954, "grad_norm": NaN, "learning_rate": 1.263878199765769e-05, "loss": 0.0, "step": 22500}, {"epoch": 3.8161606105856976, "grad_norm": NaN, "learning_rate": 1.2471473983603815e-05, "loss": 0.0, "step": 23000}, {"epoch": 3.8991206238592997, "grad_norm": NaN, "learning_rate": 1.2304165969549942e-05, "loss": 0.0, "step": 23500}, {"epoch": 3.982080637132902, "grad_norm": NaN, "learning_rate": 1.2136857955496068e-05, "loss": 0.0, "step": 24000}, {"epoch": 4.0, "eval_accuracy": 0.00019712201852946975, "eval_loss": NaN, "eval_runtime": 379.7691, "eval_samples_per_second": 13.358, "eval_steps_per_second": 0.837, "step": 24108}, {"epoch": 4.065040650406504, "grad_norm": NaN, "learning_rate": 1.1969549941442196e-05, "loss": 0.0, "step": 24500}, {"epoch": 4.148000663680106, "grad_norm": NaN, "learning_rate": 1.1802241927388323e-05, "loss": 0.0, "step": 25000}, {"epoch": 4.230960676953709, "grad_norm": NaN, "learning_rate": 1.1634933913334449e-05, "loss": 0.0, "step": 25500}, {"epoch": 4.31392069022731, "grad_norm": NaN, "learning_rate": 1.1467625899280577e-05, "loss": 0.0, "step": 26000}, {"epoch": 4.396880703500912, "grad_norm": NaN, "learning_rate": 1.1300317885226704e-05, "loss": 0.0, "step": 26500}, {"epoch": 4.479840716774515, "grad_norm": NaN, "learning_rate": 1.113300987117283e-05, "loss": 0.0, "step": 27000}, {"epoch": 4.562800730048117, "grad_norm": NaN, "learning_rate": 1.0965701857118958e-05, "loss": 0.0, "step": 27500}, {"epoch": 4.645760743321719, "grad_norm": NaN, "learning_rate": 1.0798393843065085e-05, "loss": 0.0, "step": 28000}, {"epoch": 4.728720756595321, "grad_norm": NaN, "learning_rate": 1.063108582901121e-05, "loss": 0.0, "step": 28500}, {"epoch": 4.811680769868923, "grad_norm": NaN, "learning_rate": 1.0463777814957336e-05, "loss": 0.0, "step": 29000}, {"epoch": 4.894640783142525, "grad_norm": NaN, "learning_rate": 1.0296469800903464e-05, "loss": 0.0, "step": 29500}, {"epoch": 4.9776007964161275, "grad_norm": NaN, "learning_rate": 1.012916178684959e-05, "loss": 0.0, "step": 30000}, {"epoch": 5.0, "eval_accuracy": 0.00019712201852946975, "eval_loss": NaN, "eval_runtime": 311.5574, "eval_samples_per_second": 16.283, "eval_steps_per_second": 1.021, "step": 30135}, {"epoch": 5.060560809689729, "grad_norm": NaN, "learning_rate": 9.961853772795717e-06, "loss": 0.0, "step": 30500}, {"epoch": 5.143520822963332, "grad_norm": NaN, "learning_rate": 9.794545758741845e-06, "loss": 0.0, "step": 31000}, {"epoch": 5.2264808362369335, "grad_norm": NaN, "learning_rate": 9.627237744687972e-06, "loss": 0.0, "step": 31500}, {"epoch": 5.309440849510536, "grad_norm": NaN, "learning_rate": 9.459929730634098e-06, "loss": 0.0, "step": 32000}, {"epoch": 5.392400862784138, "grad_norm": NaN, "learning_rate": 9.292621716580225e-06, "loss": 0.0, "step": 32500}, {"epoch": 5.47536087605774, "grad_norm": NaN, "learning_rate": 9.125313702526351e-06, "loss": 0.0, "step": 33000}, {"epoch": 5.558320889331342, "grad_norm": NaN, "learning_rate": 8.958005688472479e-06, "loss": 0.0, "step": 33500}, {"epoch": 5.641280902604945, "grad_norm": NaN, "learning_rate": 8.790697674418606e-06, "loss": 0.0, "step": 34000}, {"epoch": 5.724240915878546, "grad_norm": NaN, "learning_rate": 8.623389660364732e-06, "loss": 0.0, "step": 34500}, {"epoch": 5.807200929152149, "grad_norm": NaN, "learning_rate": 8.456081646310858e-06, "loss": 0.0, "step": 35000}, {"epoch": 5.890160942425751, "grad_norm": NaN, "learning_rate": 8.288773632256985e-06, "loss": 0.0, "step": 35500}, {"epoch": 5.973120955699353, "grad_norm": NaN, "learning_rate": 8.121465618203113e-06, "loss": 0.0, "step": 36000}, {"epoch": 6.0, "eval_accuracy": 0.00019712201852946975, "eval_loss": NaN, "eval_runtime": 247.0203, "eval_samples_per_second": 20.537, "eval_steps_per_second": 1.287, "step": 36162}, {"epoch": 6.056080968972955, "grad_norm": NaN, "learning_rate": 7.95415760414924e-06, "loss": 0.0, "step": 36500}, {"epoch": 6.1390409822465575, "grad_norm": NaN, "learning_rate": 7.786849590095366e-06, "loss": 0.0, "step": 37000}, {"epoch": 6.222000995520159, "grad_norm": NaN, "learning_rate": 7.6195415760414924e-06, "loss": 0.0, "step": 37500}, {"epoch": 6.304961008793762, "grad_norm": NaN, "learning_rate": 7.45223356198762e-06, "loss": 0.0, "step": 38000}, {"epoch": 6.3879210220673635, "grad_norm": NaN, "learning_rate": 7.284925547933747e-06, "loss": 0.0, "step": 38500}, {"epoch": 6.470881035340966, "grad_norm": NaN, "learning_rate": 7.1176175338798735e-06, "loss": 0.0, "step": 39000}, {"epoch": 6.553841048614568, "grad_norm": NaN, "learning_rate": 6.950309519826001e-06, "loss": 0.0, "step": 39500}, {"epoch": 6.636801061888169, "grad_norm": NaN, "learning_rate": 6.783001505772126e-06, "loss": 0.0, "step": 40000}, {"epoch": 6.719761075161772, "grad_norm": NaN, "learning_rate": 6.615693491718254e-06, "loss": 0.0, "step": 40500}, {"epoch": 6.802721088435375, "grad_norm": NaN, "learning_rate": 6.448385477664381e-06, "loss": 0.0, "step": 41000}, {"epoch": 6.885681101708976, "grad_norm": NaN, "learning_rate": 6.2810774636105074e-06, "loss": 0.0, "step": 41500}, {"epoch": 6.968641114982578, "grad_norm": NaN, "learning_rate": 6.113769449556635e-06, "loss": 0.0, "step": 42000}, {"epoch": 7.0, "eval_accuracy": 0.00019712201852946975, "eval_loss": NaN, "eval_runtime": 247.2103, "eval_samples_per_second": 20.521, "eval_steps_per_second": 1.286, "step": 42189}], "logging_steps": 500, "max_steps": 60270, "num_input_tokens_seen": 0, "num_train_epochs": 10, "save_steps": 30130, "stateful_callbacks": {"TrainerControl": {"args": {"should_epoch_stop": false, "should_evaluate": false, "should_log": false, "should_save": true, "should_training_stop": false}, "attributes": {}}}, "total_flos": 3.813430906210222e+18, "train_batch_size": 8, "trial_name": null, "trial_params": null}