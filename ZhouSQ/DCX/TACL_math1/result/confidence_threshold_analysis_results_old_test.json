[{"confidence_threshold": 0.0, "precision": 0.24228184528320953, "recall": 0.7268455358496286, "f1": 0.3634227679248143, "true_positives": 4795, "false_positives": 14996, "false_negatives": 1802}, {"confidence_threshold": 0.01, "precision": 0.24228184528320953, "recall": 0.7268455358496286, "f1": 0.3634227679248143, "true_positives": 4795, "false_positives": 14996, "false_negatives": 1802}, {"confidence_threshold": 0.02, "precision": 0.24228184528320953, "recall": 0.7268455358496286, "f1": 0.3634227679248143, "true_positives": 4795, "false_positives": 14996, "false_negatives": 1802}, {"confidence_threshold": 0.03, "precision": 0.24228184528320953, "recall": 0.7268455358496286, "f1": 0.3634227679248143, "true_positives": 4795, "false_positives": 14996, "false_negatives": 1802}, {"confidence_threshold": 0.04, "precision": 0.24228184528320953, "recall": 0.7268455358496286, "f1": 0.3634227679248143, "true_positives": 4795, "false_positives": 14996, "false_negatives": 1802}, {"confidence_threshold": 0.05, "precision": 0.24228184528320953, "recall": 0.7268455358496286, "f1": 0.3634227679248143, "true_positives": 4795, "false_positives": 14996, "false_negatives": 1802}, {"confidence_threshold": 0.06, "precision": 0.24228184528320953, "recall": 0.7268455358496286, "f1": 0.3634227679248143, "true_positives": 4795, "false_positives": 14996, "false_negatives": 1802}, {"confidence_threshold": 0.07, "precision": 0.24228184528320953, "recall": 0.7268455358496286, "f1": 0.3634227679248143, "true_positives": 4795, "false_positives": 14996, "false_negatives": 1802}, {"confidence_threshold": 0.08, "precision": 0.24228184528320953, "recall": 0.7268455358496286, "f1": 0.3634227679248143, "true_positives": 4795, "false_positives": 14996, "false_negatives": 1802}, {"confidence_threshold": 0.09, "precision": 0.24228184528320953, "recall": 0.7268455358496286, "f1": 0.3634227679248143, "true_positives": 4795, "false_positives": 14996, "false_negatives": 1802}, {"confidence_threshold": 0.1, "precision": 0.24228184528320953, "recall": 0.7268455358496286, "f1": 0.3634227679248143, "true_positives": 4795, "false_positives": 14996, "false_negatives": 1802}, {"confidence_threshold": 0.11, "precision": 0.24228184528320953, "recall": 0.7268455358496286, "f1": 0.3634227679248143, "true_positives": 4795, "false_positives": 14996, "false_negatives": 1802}, {"confidence_threshold": 0.12, "precision": 0.24228184528320953, "recall": 0.7268455358496286, "f1": 0.3634227679248143, "true_positives": 4795, "false_positives": 14996, "false_negatives": 1802}, {"confidence_threshold": 0.13, "precision": 0.24228184528320953, "recall": 0.7268455358496286, "f1": 0.3634227679248143, "true_positives": 4795, "false_positives": 14996, "false_negatives": 1802}, {"confidence_threshold": 0.14, "precision": 0.24228184528320953, "recall": 0.7268455358496286, "f1": 0.3634227679248143, "true_positives": 4795, "false_positives": 14996, "false_negatives": 1802}, {"confidence_threshold": 0.15, "precision": 0.24228184528320953, "recall": 0.7268455358496286, "f1": 0.3634227679248143, "true_positives": 4795, "false_positives": 14996, "false_negatives": 1802}, {"confidence_threshold": 0.16, "precision": 0.24228184528320953, "recall": 0.7268455358496286, "f1": 0.3634227679248143, "true_positives": 4795, "false_positives": 14996, "false_negatives": 1802}, {"confidence_threshold": 0.17, "precision": 0.24228184528320953, "recall": 0.7268455358496286, "f1": 0.3634227679248143, "true_positives": 4795, "false_positives": 14996, "false_negatives": 1802}, {"confidence_threshold": 0.18, "precision": 0.24228184528320953, "recall": 0.7268455358496286, "f1": 0.3634227679248143, "true_positives": 4795, "false_positives": 14996, "false_negatives": 1802}, {"confidence_threshold": 0.19, "precision": 0.24228184528320953, "recall": 0.7268455358496286, "f1": 0.3634227679248143, "true_positives": 4795, "false_positives": 14996, "false_negatives": 1802}, {"confidence_threshold": 0.2, "precision": 0.24228184528320953, "recall": 0.7268455358496286, "f1": 0.3634227679248143, "true_positives": 4795, "false_positives": 14996, "false_negatives": 1802}, {"confidence_threshold": 0.21, "precision": 0.24228184528320953, "recall": 0.7268455358496286, "f1": 0.3634227679248143, "true_positives": 4795, "false_positives": 14996, "false_negatives": 1802}, {"confidence_threshold": 0.22, "precision": 0.24228184528320953, "recall": 0.7268455358496286, "f1": 0.3634227679248143, "true_positives": 4795, "false_positives": 14996, "false_negatives": 1802}, {"confidence_threshold": 0.23, "precision": 0.24228184528320953, "recall": 0.7268455358496286, "f1": 0.3634227679248143, "true_positives": 4795, "false_positives": 14996, "false_negatives": 1802}, {"confidence_threshold": 0.24, "precision": 0.24228184528320953, "recall": 0.7268455358496286, "f1": 0.3634227679248143, "true_positives": 4795, "false_positives": 14996, "false_negatives": 1802}, {"confidence_threshold": 0.25, "precision": 0.24228184528320953, "recall": 0.7268455358496286, "f1": 0.3634227679248143, "true_positives": 4795, "false_positives": 14996, "false_negatives": 1802}, {"confidence_threshold": 0.26, "precision": 0.24228184528320953, "recall": 0.7268455358496286, "f1": 0.3634227679248143, "true_positives": 4795, "false_positives": 14996, "false_negatives": 1802}, {"confidence_threshold": 0.27, "precision": 0.24228184528320953, "recall": 0.7268455358496286, "f1": 0.3634227679248143, "true_positives": 4795, "false_positives": 14996, "false_negatives": 1802}, {"confidence_threshold": 0.28, "precision": 0.24228184528320953, "recall": 0.7268455358496286, "f1": 0.3634227679248143, "true_positives": 4795, "false_positives": 14996, "false_negatives": 1802}, {"confidence_threshold": 0.29, "precision": 0.2423185769153022, "recall": 0.7268455358496286, "f1": 0.3634640894447603, "true_positives": 4795, "false_positives": 14993, "false_negatives": 1802}, {"confidence_threshold": 0.3, "precision": 0.24241658240647118, "recall": 0.7268455358496286, "f1": 0.3635743261174508, "true_positives": 4795, "false_positives": 14985, "false_negatives": 1802}, {"confidence_threshold": 0.31, "precision": 0.24263738488007286, "recall": 0.7268455358496286, "f1": 0.36382260328540533, "true_positives": 4795, "false_positives": 14967, "false_negatives": 1802}, {"confidence_threshold": 0.32, "precision": 0.24282024008509345, "recall": 0.726693951796271, "f1": 0.36400911161731203, "true_positives": 4794, "false_positives": 14949, "false_negatives": 1803}, {"confidence_threshold": 0.33, "precision": 0.24316371569174572, "recall": 0.7265423677429135, "f1": 0.36437585525315497, "true_positives": 4793, "false_positives": 14918, "false_negatives": 1804}, {"confidence_threshold": 0.34, "precision": 0.2437684403296368, "recall": 0.7263907836895559, "f1": 0.36503523138449817, "true_positives": 4792, "false_positives": 14866, "false_negatives": 1805}, {"confidence_threshold": 0.35, "precision": 0.24462708663025168, "recall": 0.7263907836895559, "f1": 0.3659970976857863, "true_positives": 4792, "false_positives": 14797, "false_negatives": 1805}, {"confidence_threshold": 0.36, "precision": 0.2455915521837195, "recall": 0.7262391996361983, "f1": 0.36705611951733386, "true_positives": 4791, "false_positives": 14717, "false_negatives": 1806}, {"confidence_threshold": 0.37, "precision": 0.24672173464119773, "recall": 0.7244201909959073, "f1": 0.3680825663341934, "true_positives": 4779, "false_positives": 14591, "false_negatives": 1818}, {"confidence_threshold": 0.38, "precision": 0.24817822194461794, "recall": 0.7227527664089738, "f1": 0.3694835135030416, "true_positives": 4768, "false_positives": 14444, "false_negatives": 1829}, {"confidence_threshold": 0.39, "precision": 0.25005255413075467, "recall": 0.7212369258753979, "f1": 0.3713560975609756, "true_positives": 4758, "false_positives": 14270, "false_negatives": 1839}, {"confidence_threshold": 0.4, "precision": 0.25200360915025743, "recall": 0.719721085341822, "f1": 0.37329978771916034, "true_positives": 4748, "false_positives": 14093, "false_negatives": 1849}, {"confidence_threshold": 0.41, "precision": 0.2546473409127647, "recall": 0.7163862361679552, "f1": 0.3757354110351407, "true_positives": 4726, "false_positives": 13833, "false_negatives": 1871}, {"confidence_threshold": 0.42, "precision": 0.25812818685234934, "recall": 0.7136577232075185, "f1": 0.3791270736028346, "true_positives": 4708, "false_positives": 13531, "false_negatives": 1889}, {"confidence_threshold": 0.43, "precision": 0.26218318133497454, "recall": 0.7103228740336517, "f1": 0.3829995913363302, "true_positives": 4686, "false_positives": 13187, "false_negatives": 1911}, {"confidence_threshold": 0.44, "precision": 0.26696858518688377, "recall": 0.7059269364862817, "f1": 0.38742148829083656, "true_positives": 4657, "false_positives": 12787, "false_negatives": 1940}, {"confidence_threshold": 0.45, "precision": 0.2715461493239271, "recall": 0.7001667424586934, "f1": 0.39132460710806116, "true_positives": 4619, "false_positives": 12391, "false_negatives": 1978}, {"confidence_threshold": 0.46, "precision": 0.27744789142026177, "recall": 0.6941033803243899, "f1": 0.3964330548461106, "true_positives": 4579, "false_positives": 11925, "false_negatives": 2018}, {"confidence_threshold": 0.47, "precision": 0.2844053969250078, "recall": 0.6869789298165833, "f1": 0.4022723238061423, "true_positives": 4532, "false_positives": 11403, "false_negatives": 2065}, {"confidence_threshold": 0.48, "precision": 0.29246946639670823, "recall": 0.6787933909352736, "f1": 0.4088004381960927, "true_positives": 4478, "false_positives": 10833, "false_negatives": 2119}, {"confidence_threshold": 0.49, "precision": 0.301585132549877, "recall": 0.6690920115203881, "f1": 0.4157679084444026, "true_positives": 4414, "false_positives": 10222, "false_negatives": 2183}, {"confidence_threshold": 0.5, "precision": 0.3129046180405697, "recall": 0.6593906321055025, "f1": 0.42441094687545733, "true_positives": 4350, "false_positives": 9552, "false_negatives": 2247}, {"confidence_threshold": 0.51, "precision": 0.32681884113291093, "recall": 0.648931332423829, "f1": 0.43470755483346873, "true_positives": 4281, "false_positives": 8818, "false_negatives": 2316}, {"confidence_threshold": 0.52, "precision": 0.3410689738821056, "recall": 0.639381537062301, "f1": 0.44484286015608526, "true_positives": 4218, "false_positives": 8149, "false_negatives": 2379}, {"confidence_threshold": 0.53, "precision": 0.35816494666551035, "recall": 0.6260421403668334, "f1": 0.455648720211827, "true_positives": 4130, "false_positives": 7401, "false_negatives": 2467}, {"confidence_threshold": 0.54, "precision": 0.37412750116333177, "recall": 0.6093678944974988, "f1": 0.4636143466728174, "true_positives": 4020, "false_positives": 6725, "false_negatives": 2577}, {"confidence_threshold": 0.55, "precision": 0.3923123243677238, "recall": 0.5925420645748067, "f1": 0.47207294245516573, "true_positives": 3909, "false_positives": 6055, "false_negatives": 2688}, {"confidence_threshold": 0.56, "precision": 0.4123006833712984, "recall": 0.5761709868121874, "f1": 0.48065250379362673, "true_positives": 3801, "false_positives": 5418, "false_negatives": 2796}, {"confidence_threshold": 0.57, "precision": 0.4329239489844119, "recall": 0.5557071396089132, "f1": 0.486691005642217, "true_positives": 3666, "false_positives": 4802, "false_negatives": 2931}, {"confidence_threshold": 0.58, "precision": 0.45605242868157286, "recall": 0.5379718053660755, "f1": 0.49363655330690587, "true_positives": 3549, "false_positives": 4233, "false_negatives": 3048}, {"confidence_threshold": 0.59, "precision": 0.4818374558303887, "recall": 0.5167500378960134, "f1": 0.49868344060854297, "true_positives": 3409, "false_positives": 3666, "false_negatives": 3188}, {"confidence_threshold": 0.6, "precision": 0.5044766903365236, "recall": 0.4953766863725936, "f1": 0.49988527724665394, "true_positives": 3268, "false_positives": 3210, "false_negatives": 3329}, {"confidence_threshold": 0.61, "precision": 0.5305148312308217, "recall": 0.47172957404881005, "f1": 0.4993982187274332, "true_positives": 3112, "false_positives": 2754, "false_negatives": 3485}, {"confidence_threshold": 0.62, "precision": 0.5575768942235559, "recall": 0.4506593906321055, "f1": 0.4984491575152989, "true_positives": 2973, "false_positives": 2359, "false_negatives": 3624}, {"confidence_threshold": 0.63, "precision": 0.5809602993140719, "recall": 0.42367742913445505, "f1": 0.49000701262272084, "true_positives": 2795, "false_positives": 2016, "false_negatives": 3802}, {"confidence_threshold": 0.64, "precision": 0.6053174421269768, "recall": 0.4003334849173867, "f1": 0.48193430656934305, "true_positives": 2641, "false_positives": 1722, "false_negatives": 3956}, {"confidence_threshold": 0.65, "precision": 0.6353152229625832, "recall": 0.37577686827345763, "f1": 0.47223545099533293, "true_positives": 2479, "false_positives": 1423, "false_negatives": 4118}, {"confidence_threshold": 0.66, "precision": 0.657404755084503, "recall": 0.3478854024556617, "f1": 0.45499603489294216, "true_positives": 2295, "false_positives": 1196, "false_negatives": 4302}, {"confidence_threshold": 0.67, "precision": 0.6836340206185567, "recall": 0.32166136122479916, "f1": 0.4374806720956603, "true_positives": 2122, "false_positives": 982, "false_negatives": 4475}, {"confidence_threshold": 0.68, "precision": 0.7074756229685807, "recall": 0.2969531605275125, "f1": 0.4183215887251762, "true_positives": 1959, "false_positives": 810, "false_negatives": 4638}, {"confidence_threshold": 0.69, "precision": 0.727199027158492, "recall": 0.2719417917235107, "f1": 0.3958517210944395, "true_positives": 1794, "false_positives": 673, "false_negatives": 4803}, {"confidence_threshold": 0.7, "precision": 0.7402538531278332, "recall": 0.2475367591329392, "f1": 0.37100988299443366, "true_positives": 1633, "false_positives": 573, "false_negatives": 4964}, {"confidence_threshold": 0.71, "precision": 0.7603305785123967, "recall": 0.22313172654236774, "f1": 0.3450134770889488, "true_positives": 1472, "false_positives": 464, "false_negatives": 5125}, {"confidence_threshold": 0.72, "precision": 0.7761542957334892, "recall": 0.20130362285887524, "f1": 0.31969186326432353, "true_positives": 1328, "false_positives": 383, "false_negatives": 5269}, {"confidence_threshold": 0.73, "precision": 0.7906517445687953, "recall": 0.18205244808246174, "f1": 0.2959586002957122, "true_positives": 1201, "false_positives": 318, "false_negatives": 5396}, {"confidence_threshold": 0.74, "precision": 0.8079571537872992, "recall": 0.16007276034561163, "f1": 0.26720647773279355, "true_positives": 1056, "false_positives": 251, "false_negatives": 5541}, {"confidence_threshold": 0.75, "precision": 0.810405643738977, "recall": 0.13930574503562226, "f1": 0.23774414694088736, "true_positives": 919, "false_positives": 215, "false_negatives": 5678}, {"confidence_threshold": 0.76, "precision": 0.8199152542372882, "recall": 0.11732605729877217, "f1": 0.2052778146134465, "true_positives": 774, "false_positives": 170, "false_negatives": 5823}, {"confidence_threshold": 0.77, "precision": 0.8409090909090909, "recall": 0.1009549795361528, "f1": 0.1802679658952497, "true_positives": 666, "false_positives": 126, "false_negatives": 5931}, {"confidence_threshold": 0.78, "precision": 0.846875, "recall": 0.08215855691981204, "f1": 0.14978582285477407, "true_positives": 542, "false_positives": 98, "false_negatives": 6055}, {"confidence_threshold": 0.79, "precision": 0.8554913294797688, "recall": 0.06730331969076853, "f1": 0.12478920741989881, "true_positives": 444, "false_positives": 75, "false_negatives": 6153}, {"confidence_threshold": 0.8, "precision": 0.8693586698337292, "recall": 0.05547976352887676, "f1": 0.10430322029068112, "true_positives": 366, "false_positives": 55, "false_negatives": 6231}, {"confidence_threshold": 0.81, "precision": 0.8496932515337423, "recall": 0.04198878278005154, "f1": 0.08002311136790409, "true_positives": 277, "false_positives": 49, "false_negatives": 6320}, {"confidence_threshold": 0.82, "precision": 0.861003861003861, "recall": 0.033803243898741855, "f1": 0.06505250875145858, "true_positives": 223, "false_positives": 36, "false_negatives": 6374}, {"confidence_threshold": 0.83, "precision": 0.8586956521739131, "recall": 0.02395028043049871, "f1": 0.04660079634272231, "true_positives": 158, "false_positives": 26, "false_negatives": 6439}, {"confidence_threshold": 0.84, "precision": 0.8897058823529411, "recall": 0.018341670456268, "f1": 0.03594237338482103, "true_positives": 121, "false_positives": 15, "false_negatives": 6476}, {"confidence_threshold": 0.85, "precision": 0.92, "recall": 0.010459299681673489, "f1": 0.020683453237410075, "true_positives": 69, "false_positives": 6, "false_negatives": 6528}, {"confidence_threshold": 0.86, "precision": 0.8611111111111112, "recall": 0.00469910565408519, "f1": 0.009347203377054122, "true_positives": 31, "false_positives": 5, "false_negatives": 6566}, {"confidence_threshold": 0.87, "precision": 0.875, "recall": 0.002122176747006215, "f1": 0.004234084379252987, "true_positives": 14, "false_positives": 2, "false_negatives": 6583}, {"confidence_threshold": 0.88, "precision": 0.9, "recall": 0.001364256480218281, "f1": 0.0027243832299076736, "true_positives": 9, "false_positives": 1, "false_negatives": 6588}, {"confidence_threshold": 0.89, "precision": 1.0, "recall": 0.0006063362134303471, "f1": 0.0012119375852143615, "true_positives": 4, "false_positives": 0, "false_negatives": 6593}, {"confidence_threshold": 0.9, "precision": 0, "recall": 0.0, "f1": 0, "true_positives": 0, "false_positives": 0, "false_negatives": 6597}, {"confidence_threshold": 0.91, "precision": 0, "recall": 0.0, "f1": 0, "true_positives": 0, "false_positives": 0, "false_negatives": 6597}, {"confidence_threshold": 0.92, "precision": 0, "recall": 0.0, "f1": 0, "true_positives": 0, "false_positives": 0, "false_negatives": 6597}, {"confidence_threshold": 0.93, "precision": 0, "recall": 0.0, "f1": 0, "true_positives": 0, "false_positives": 0, "false_negatives": 6597}, {"confidence_threshold": 0.94, "precision": 0, "recall": 0.0, "f1": 0, "true_positives": 0, "false_positives": 0, "false_negatives": 6597}, {"confidence_threshold": 0.95, "precision": 0, "recall": 0.0, "f1": 0, "true_positives": 0, "false_positives": 0, "false_negatives": 6597}, {"confidence_threshold": 0.96, "precision": 0, "recall": 0.0, "f1": 0, "true_positives": 0, "false_positives": 0, "false_negatives": 6597}, {"confidence_threshold": 0.97, "precision": 0, "recall": 0.0, "f1": 0, "true_positives": 0, "false_positives": 0, "false_negatives": 6597}, {"confidence_threshold": 0.98, "precision": 0, "recall": 0.0, "f1": 0, "true_positives": 0, "false_positives": 0, "false_negatives": 6597}, {"confidence_threshold": 0.99, "precision": 0, "recall": 0.0, "f1": 0, "true_positives": 0, "false_positives": 0, "false_negatives": 6597}, {"confidence_threshold": 1.0, "precision": 0, "recall": 0.0, "f1": 0, "true_positives": 0, "false_positives": 0, "false_negatives": 6597}, {"confidence_threshold": 1.01, "precision": 0, "recall": 0.0, "f1": 0, "true_positives": 0, "false_positives": 0, "false_negatives": 6597}, {"confidence_threshold": 1.02, "precision": 0, "recall": 0.0, "f1": 0, "true_positives": 0, "false_positives": 0, "false_negatives": 6597}, {"confidence_threshold": 1.03, "precision": 0, "recall": 0.0, "f1": 0, "true_positives": 0, "false_positives": 0, "false_negatives": 6597}, {"confidence_threshold": 1.04, "precision": 0, "recall": 0.0, "f1": 0, "true_positives": 0, "false_positives": 0, "false_negatives": 6597}]