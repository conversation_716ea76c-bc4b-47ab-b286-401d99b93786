"""
混合匹配器 - 结合检索和分类的混合方案
"""

import numpy as np
from typing import List, Dict, Tuple, Optional
import os
import json
from datetime import datetime

from similarity_matcher import SimilarityMatcher
from classification_model import ClassificationModel
from data_loader import DataLoader
import config

class HybridMatcher:
    def __init__(self):
        self.similarity_matcher = SimilarityMatcher()
        self.classification_model = ClassificationModel()
        self.data_loader = DataLoader()
        
        # 混合策略参数
        self.similarity_threshold = config.SIMILARITY_THRESHOLD  # 高置信度阈值
        self.classification_threshold = 0.8  # 分类模型置信度阈值
        self.use_classification_fallback = True  # 是否使用分类模型作为后备
        
        # 模型状态
        self.similarity_ready = False
        self.classification_ready = False
        
    def build_similarity_knowledge_base(self, use_cache: bool = True):
        """构建相似度匹配的知识库"""
        print("构建相似度匹配知识库...")
        self.similarity_matcher.build_knowledge_base(use_cache=use_cache)
        self.similarity_ready = True
        print("相似度匹配知识库构建完成!")
    
    def train_classification_model(self, force_retrain: bool = False):
        """训练分类模型"""
        model_dir = os.path.join(config.OUTPUT_DIR, "classification_model")
        
        # 检查是否已有训练好的模型
        if not force_retrain and os.path.exists(model_dir) and os.path.exists(os.path.join(model_dir, "config.json")):
            print("发现已训练的分类模型，正在加载...")
            try:
                self.classification_model.load_trained_model(model_dir)
                self.classification_ready = True
                print("分类模型加载成功!")
                return
            except Exception as e:
                print(f"加载已训练模型失败: {e}，将重新训练")
        
        print("开始训练分类模型...")
        
        # 加载训练数据和验证数据
        train_texts, train_labels = self.data_loader.create_training_dataset()
        val_texts, val_labels = self.data_loader.get_doc_tokens_and_labels(self.data_loader.val_data or [])

        # 如果没有验证集，从训练集分割一部分
        if not val_texts:
            print("未找到验证集，从训练集分割20%作为验证集")
            split_idx = int(len(train_texts) * 0.8)
            val_texts = train_texts[split_idx:]
            val_labels = train_labels[split_idx:]
            train_texts = train_texts[:split_idx]
            train_labels = train_labels[:split_idx]

        # 获取唯一标签数量
        unique_labels = set()
        for label_list in train_labels:
            unique_labels.add(" -> ".join(label_list))
        for label_list in val_labels:
            unique_labels.add(" -> ".join(label_list))
        num_labels = len(unique_labels)

        print(f"训练数据: {len(train_texts)} 个样本")
        print(f"验证数据: {len(val_texts)} 个样本")
        print(f"唯一标签数量: {num_labels}")

        # 加载模型
        self.classification_model.load_model(num_labels)

        # 准备训练数据
        train_dataset, val_dataset = self.classification_model.prepare_training_data(
            train_texts, train_labels, val_texts, val_labels)
        
        # 训练
        self.classification_model.train(train_dataset, val_dataset)
        
        self.classification_ready = True
        print("分类模型训练完成!")

    def add_new_data_to_similarity(self, new_texts: List[str], new_labels: List[List[str]]):
        """
        向相似度匹配器添加新数据
        """
        if not self.similarity_ready:
            print("相似度匹配器未准备就绪，请先构建知识库")
            return

        self.similarity_matcher.add_new_data(new_texts, new_labels, save_cache=True)
        print("相似度匹配器数据已更新")

    def add_new_data_from_file(self, file_path: str, retrain_classification: bool = False):
        """
        从文件添加新数据并选择性重训练分类模型
        """
        print(f"从文件添加新数据: {file_path}")

        # 加载新数据
        new_data = self.data_loader.load_json_data(file_path)
        new_texts, new_labels = self.data_loader.get_doc_tokens_and_labels(new_data)

        # 添加到相似度匹配器
        if self.similarity_ready:
            self.add_new_data_to_similarity(new_texts, new_labels)

        # 选择性重训练分类模型
        if retrain_classification and self.classification_ready:
            print("重新训练分类模型以包含新数据...")
            self.train_classification_model(force_retrain=True)
        elif retrain_classification:
            print("分类模型未准备就绪，跳过重训练")

        print("新数据添加完成!")

    def get_knowledge_base_stats(self):
        """
        获取知识库统计信息
        """
        stats = {}

        if self.similarity_ready and self.similarity_matcher.train_texts:
            stats['similarity_samples'] = len(self.similarity_matcher.train_texts)
            stats['similarity_ready'] = True
        else:
            stats['similarity_samples'] = 0
            stats['similarity_ready'] = False

        stats['classification_ready'] = self.classification_ready

        return stats
    
    def predict_with_strategy(self, query_text: str, strategy: str = "auto") -> Dict:
        """
        使用指定策略进行预测
        
        Args:
            query_text: 查询文本
            strategy: 预测策略
                - "similarity": 仅使用相似度匹配
                - "classification": 仅使用分类模型
                - "auto": 自动选择最佳策略
                - "ensemble": 集成两种方法
        """
        result = {
            'query_text': query_text,
            'strategy_used': strategy,
            'similarity_result': None,
            'classification_result': None,
            'final_prediction': None,
            'confidence': 0.0,
            'reasoning': ""
        }
        
        if strategy == "similarity" or strategy == "auto" or strategy == "ensemble":
            if self.similarity_ready:
                # 相似度匹配
                similar_samples = self.similarity_matcher.find_most_similar(query_text, top_k=1)
                if similar_samples:
                    best_match = similar_samples[0]
                    result['similarity_result'] = {
                        'predicted_label': best_match['matched_label'],
                        'confidence': float(best_match['similarity']),
                        'matched_text': best_match['matched_text']
                    }
        
        if strategy == "classification" or strategy == "auto" or strategy == "ensemble":
            if self.classification_ready:
                # 分类模型预测
                pred_label, confidence = self.classification_model.predict_single(query_text)
                result['classification_result'] = {
                    'predicted_label': pred_label,
                    'confidence': float(confidence)
                }
        
        # 决策逻辑
        if strategy == "similarity":
            if result['similarity_result']:
                result['final_prediction'] = result['similarity_result']['predicted_label']
                result['confidence'] = result['similarity_result']['confidence']
                result['reasoning'] = "使用相似度匹配"
            else:
                result['reasoning'] = "相似度匹配失败"
                
        elif strategy == "classification":
            if result['classification_result']:
                result['final_prediction'] = result['classification_result']['predicted_label']
                result['confidence'] = result['classification_result']['confidence']
                result['reasoning'] = "使用分类模型"
            else:
                result['reasoning'] = "分类模型预测失败"
                
        elif strategy == "auto":
            # 自动选择策略
            similarity_conf = result['similarity_result']['confidence'] if result['similarity_result'] else 0.0
            classification_conf = result['classification_result']['confidence'] if result['classification_result'] else 0.0
            
            if similarity_conf >= self.similarity_threshold:
                # 高相似度，使用相似度匹配
                result['final_prediction'] = result['similarity_result']['predicted_label']
                result['confidence'] = similarity_conf
                result['reasoning'] = f"相似度高({similarity_conf:.3f} >= {self.similarity_threshold})，使用相似度匹配"
                result['strategy_used'] = "similarity"
                
            elif classification_conf >= self.classification_threshold and self.use_classification_fallback:
                # 相似度不够高，但分类置信度高，使用分类模型
                result['final_prediction'] = result['classification_result']['predicted_label']
                result['confidence'] = classification_conf
                result['reasoning'] = f"相似度较低({similarity_conf:.3f})，使用分类模型(置信度:{classification_conf:.3f})"
                result['strategy_used'] = "classification"
                
            elif similarity_conf > 0:
                # 都不够高，但有相似度结果，使用相似度匹配
                result['final_prediction'] = result['similarity_result']['predicted_label']
                result['confidence'] = similarity_conf
                result['reasoning'] = f"两种方法置信度都不高，使用相似度匹配({similarity_conf:.3f})"
                result['strategy_used'] = "similarity"
                
            else:
                result['reasoning'] = "所有方法都失败"
                
        elif strategy == "ensemble":
            # 集成方法
            if result['similarity_result'] and result['classification_result']:
                sim_conf = result['similarity_result']['confidence']
                cls_conf = result['classification_result']['confidence']
                
                # 加权平均置信度
                total_weight = sim_conf + cls_conf
                if total_weight > 0:
                    sim_weight = sim_conf / total_weight
                    cls_weight = cls_conf / total_weight
                    
                    # 如果两个方法预测相同，增强置信度
                    if result['similarity_result']['predicted_label'] == result['classification_result']['predicted_label']:
                        result['final_prediction'] = result['similarity_result']['predicted_label']
                        result['confidence'] = min(1.0, (sim_conf + cls_conf) / 2 * 1.2)  # 增强置信度
                        result['reasoning'] = "两种方法预测一致，集成结果"
                    else:
                        # 选择置信度更高的
                        if sim_conf >= cls_conf:
                            result['final_prediction'] = result['similarity_result']['predicted_label']
                            result['confidence'] = sim_conf
                            result['reasoning'] = f"集成方法：相似度置信度更高({sim_conf:.3f} vs {cls_conf:.3f})"
                        else:
                            result['final_prediction'] = result['classification_result']['predicted_label']
                            result['confidence'] = cls_conf
                            result['reasoning'] = f"集成方法：分类置信度更高({cls_conf:.3f} vs {sim_conf:.3f})"
        
        return result
    
    def predict(self, query_text: str) -> List[str]:
        """简化的预测接口，返回预测标签"""
        result = self.predict_with_strategy(query_text, strategy="auto")
        return result['final_prediction'] if result['final_prediction'] else []
    
    def batch_predict(self, query_texts: List[str], strategy: str = "auto") -> List[Dict]:
        """批量预测"""
        results = []
        for i, query_text in enumerate(query_texts):
            if i % 10 == 0:
                print(f"处理进度: {i+1}/{len(query_texts)}")
            
            result = self.predict_with_strategy(query_text, strategy)
            results.append(result)
        
        return results
    
    def evaluate_on_test_set(self, strategy: str = "auto") -> Dict:
        """在测试集上评估混合方法"""
        from tqdm import tqdm

        test_texts, test_labels = self.data_loader.create_test_dataset()
        print(f"测试集大小: {len(test_texts)}")

        correct_predictions = 0
        total_predictions = len(test_texts)
        detailed_results = []

        strategy_usage = {"similarity": 0, "classification": 0, "failed": 0}

        # 使用统一的进度条
        progress_bar = tqdm(
            zip(test_texts, test_labels),
            total=total_predictions,
            desc=f"评估({strategy}策略)",
            leave=True
        )

        for test_text, true_label in progress_bar:
            result = self.predict_with_strategy(test_text, strategy)
            predicted_label = result['final_prediction']

            # 统计策略使用情况
            if result['final_prediction']:
                strategy_usage[result['strategy_used']] += 1
            else:
                strategy_usage["failed"] += 1

            # 检查预测是否正确
            is_correct = predicted_label == true_label if predicted_label else False
            if is_correct:
                correct_predictions += 1

            # 更新进度条显示
            accuracy_so_far = correct_predictions / len(detailed_results) if detailed_results else 0
            progress_bar.set_postfix({
                '准确率': f'{accuracy_so_far:.3f}',
                '正确': correct_predictions,
                '策略': result.get('strategy_used', 'unknown')[:3]
            })

            detailed_results.append({
                'test_text': test_text,
                'true_label': true_label,
                'predicted_label': predicted_label,
                'is_correct': is_correct,
                'confidence': result['confidence'],
                'strategy_used': result['strategy_used'],
                'reasoning': result['reasoning']
            })
        
        accuracy = correct_predictions / total_predictions
        
        evaluation_result = {
            'accuracy': accuracy,
            'correct_predictions': correct_predictions,
            'total_predictions': total_predictions,
            'strategy_usage': strategy_usage,
            'detailed_results': detailed_results,
            'evaluation_strategy': strategy
        }
        
        return evaluation_result
    
    def save_evaluation_report(self, evaluation_result: Dict, filename: str = None):
        """保存评估报告"""
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"hybrid_evaluation_{timestamp}.json"
        
        filepath = os.path.join(config.RESULTS_DIR, filename)
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(evaluation_result, f, ensure_ascii=False, indent=2)
        
        print(f"评估报告已保存到: {filepath}")
        return filepath
