"""
嵌入模型模块 - 文本向量化核心组件
功能: 使用Qwen3-Embedding-4B将文本转换为高维向量
"""

import torch
import numpy as np
from transformers import AutoTokenizer, AutoModel
from typing import List
import os
import pickle
import logging
from tqdm import tqdm
import config

logger = logging.getLogger(__name__)

class EmbeddingModel:
    """文本嵌入模型 - 负责将文本转换为向量表示"""

    def __init__(self):
        self.model_path = config.EMBEDDING_MODEL_PATH
        self.device = torch.device(config.DEVICE)
        self.max_length = config.EMBEDDING_MAX_LENGTH
        self.batch_size = config.EMBEDDING_BATCH_SIZE

        # 模型组件
        self.tokenizer = None
        self.model = None

    def load_model(self):
        """加载嵌入模型"""
        if self.model is not None:
            return  # 已加载

        try:
            logger.info(f"加载嵌入模型: {self.model_path}")
            self.tokenizer = AutoTokenizer.from_pretrained(
                self.model_path,
                trust_remote_code=True
            )
            self.model = AutoModel.from_pretrained(
                self.model_path,
                trust_remote_code=True
            )
            self.model.to(self.device)
            self.model.eval()
            logger.info("嵌入模型加载完成")
        except Exception as e:
            logger.error(f"嵌入模型加载失败: {e}")
            raise

    def encode_texts(self, texts: List[str], show_progress: bool = True, desc: str = "编码文本") -> np.ndarray:
        """将文本列表编码为嵌入向量矩阵"""
        if not texts:
            return np.array([])

        # 确保模型已加载
        if self.model is None:
            self.load_model()

        all_embeddings = []

        # 只在批量处理时显示进度条
        if show_progress and len(texts) > 1:
            iterator = tqdm(range(0, len(texts), self.batch_size), desc=desc, leave=False)
        else:
            iterator = range(0, len(texts), self.batch_size)

        with torch.no_grad():
            for i in iterator:
                batch_texts = texts[i:i + self.batch_size]

                # 分词
                inputs = self.tokenizer(
                    batch_texts,
                    padding=True,
                    truncation=True,
                    max_length=self.max_length,
                    return_tensors="pt"
                ).to(self.device)

                # 获取嵌入
                outputs = self.model(**inputs)

                # 平均池化
                embeddings = outputs.last_hidden_state.mean(dim=1)

                # L2归一化
                embeddings = torch.nn.functional.normalize(embeddings, p=2, dim=1)
                all_embeddings.append(embeddings.cpu().numpy())

        return np.vstack(all_embeddings)

    def encode_single_text(self, text: str) -> np.ndarray:
        """编码单个文本 - 不显示进度条"""
        return self.encode_texts([text], show_progress=False)[0]

    def save_embeddings_cache(self, embeddings: np.ndarray, texts: List[str], cache_name: str = "embeddings"):
        """保存嵌入向量到缓存"""
        cache_file = config.EMBEDDINGS_CACHE_DIR / f"{cache_name}.pkl"

        cache_data = {
            'embeddings': embeddings,
            'texts': texts,
            'model_path': self.model_path,
            'config_hash': self._get_config_hash()
        }

        with open(cache_file, 'wb') as f:
            pickle.dump(cache_data, f)

        logger.info(f"嵌入向量缓存已保存: {cache_file}")

    def load_embeddings_cache(self, cache_name: str = "embeddings"):
        """从缓存加载嵌入向量"""
        cache_file = config.EMBEDDINGS_CACHE_DIR / f"{cache_name}.pkl"

        if not cache_file.exists():
            return None, None

        try:
            with open(cache_file, 'rb') as f:
                cache_data = pickle.load(f)

            # 验证缓存有效性 - 只检查关键配置
            if cache_data.get('model_path') != self.model_path:
                logger.warning(f"模型路径不匹配: {cache_data.get('model_path')} vs {self.model_path}")
                return None, None

            # 检查文本数量是否匹配（更重要的验证）
            cached_texts = cache_data.get('texts', [])
            if len(cached_texts) == 0:
                logger.warning("缓存中没有文本数据")
                return None, None

            logger.info(f"从缓存加载嵌入向量: {cache_file}")
            return cache_data['embeddings'], cache_data['texts']

        except Exception as e:
            logger.error(f"加载嵌入向量缓存失败: {e}")
            return None, None

    def compute_cosine_similarity(self, query_embedding: np.ndarray, database_embeddings: np.ndarray) -> np.ndarray:
        """计算余弦相似度"""
        # 确保向量已归一化（我们的嵌入已经归一化了）
        if query_embedding.ndim == 1:
            query_embedding = query_embedding.reshape(1, -1)

        # 计算余弦相似度（归一化向量的点积）
        similarities = np.dot(query_embedding, database_embeddings.T)
        return similarities.flatten()

    def _get_config_hash(self) -> str:
        """获取配置哈希，用于验证缓存有效性"""
        import hashlib
        config_str = f"{self.model_path}_{self.max_length}_{self.batch_size}"
        return hashlib.md5(config_str.encode()).hexdigest()
