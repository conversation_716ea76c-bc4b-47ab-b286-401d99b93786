"""
预测生成模块 - 根据阈值自动选择最佳策略进行预测
"""

from typing import List, Dict, Optional
import logging

logger = logging.getLogger(__name__)

class Predictor:
    """预测器 - 根据阈值自动选择向量检索或模型分类"""
    
    def __init__(self, config):
        self.config = config
    
    def predict(self, query: str, vector_builder, model_trainer, return_details: bool = False) -> Dict:
        """
        智能预测 - 根据相似度阈值自动选择策略
        
        核心逻辑：
        1. 首先使用向量检索找到最相似的样本
        2. 如果相似度 >= 阈值，直接返回向量检索结果
        3. 如果相似度 < 阈值，使用LoRA微调模型进行分类
        
        Args:
            query: 查询文本
            vector_builder: 向量构建器
            model_trainer: 模型训练器
            return_details: 是否返回详细信息
            
        Returns:
            Dict: 预测结果
        """
        result = {
            'query': query,
            'prediction': None,
            'confidence': 0.0,
            'strategy_used': None,
            'reasoning': ""
        }
        
        if return_details:
            result['details'] = {}
        
        try:
            # 第一步：向量检索
            logger.debug(f"对查询进行向量检索: {query[:50]}...")
            similar_samples = vector_builder.find_similar(query, top_k=1)
            
            if not similar_samples:
                result['reasoning'] = "向量检索失败，无法找到相似样本"
                logger.warning("向量检索失败")
                return result
            
            best_match = similar_samples[0]
            similarity = best_match['similarity']
            
            if return_details:
                result['details']['vector_retrieval'] = {
                    'similarity': similarity,
                    'matched_text': best_match['text'],
                    'matched_label': best_match['label']
                }
            
            # 第二步：根据阈值决策
            if similarity >= self.config.SIMILARITY_THRESHOLD:
                # 高相似度：使用向量检索结果
                result['prediction'] = best_match['label']
                result['confidence'] = similarity
                result['strategy_used'] = 'vector_retrieval'
                result['reasoning'] = f"相似度 {similarity:.3f} >= 阈值 {self.config.SIMILARITY_THRESHOLD}，使用向量检索"
                
                logger.debug(f"使用向量检索，相似度: {similarity:.3f}")
                
            else:
                # 低相似度：使用模型分类
                logger.debug(f"相似度 {similarity:.3f} < 阈值 {self.config.SIMILARITY_THRESHOLD}，使用模型分类")
                
                try:
                    predicted_label, model_confidence = model_trainer.predict_single(query)
                    
                    result['prediction'] = predicted_label
                    result['confidence'] = model_confidence
                    result['strategy_used'] = 'model_classification'
                    result['reasoning'] = f"相似度 {similarity:.3f} < 阈值 {self.config.SIMILARITY_THRESHOLD}，使用LoRA微调模型"
                    
                    if return_details:
                        result['details']['model_classification'] = {
                            'predicted_label': predicted_label,
                            'confidence': model_confidence
                        }
                    
                    logger.debug(f"使用模型分类，置信度: {model_confidence:.3f}")
                    
                except Exception as e:
                    # 模型分类失败，回退到向量检索
                    logger.warning(f"模型分类失败: {e}，回退到向量检索")
                    
                    result['prediction'] = best_match['label']
                    result['confidence'] = similarity
                    result['strategy_used'] = 'vector_retrieval_fallback'
                    result['reasoning'] = f"模型分类失败，回退到向量检索（相似度: {similarity:.3f}）"
                    
                    if return_details:
                        result['details']['fallback_error'] = str(e)
            
            return result
            
        except Exception as e:
            logger.error(f"预测过程出错: {e}")
            result['reasoning'] = f"预测失败: {str(e)}"
            return result
    
    def batch_predict(self, queries: List[str], vector_builder, model_trainer, return_details: bool = False) -> List[Dict]:
        """
        批量预测
        
        Args:
            queries: 查询文本列表
            vector_builder: 向量构建器
            model_trainer: 模型训练器
            return_details: 是否返回详细信息
            
        Returns:
            List[Dict]: 预测结果列表
        """
        results = []
        
        logger.info(f"开始批量预测，共 {len(queries)} 个查询")
        
        for i, query in enumerate(queries):
            if i % 10 == 0:
                logger.info(f"批量预测进度: {i+1}/{len(queries)}")
            
            result = self.predict(query, vector_builder, model_trainer, return_details)
            results.append(result)
        
        # 统计策略使用情况
        strategy_stats = {}
        for result in results:
            strategy = result.get('strategy_used', 'unknown')
            strategy_stats[strategy] = strategy_stats.get(strategy, 0) + 1
        
        logger.info(f"批量预测完成，策略使用统计: {strategy_stats}")
        
        return results
    
    def explain_prediction(self, query: str, vector_builder, model_trainer) -> Dict:
        """
        详细解释预测过程
        
        Args:
            query: 查询文本
            vector_builder: 向量构建器
            model_trainer: 模型训练器
            
        Returns:
            Dict: 详细的预测解释
        """
        explanation = {
            'query': query,
            'threshold': self.config.SIMILARITY_THRESHOLD,
            'process': []
        }
        
        try:
            # 步骤1：向量检索
            explanation['process'].append("步骤1: 向量检索")
            similar_samples = vector_builder.find_similar(query, top_k=3)  # 获取前3个相似样本
            
            if similar_samples:
                best_match = similar_samples[0]
                similarity = best_match['similarity']
                
                explanation['vector_retrieval'] = {
                    'top_matches': similar_samples,
                    'best_similarity': similarity,
                    'best_match_label': best_match['label']
                }
                
                explanation['process'].append(f"找到最相似样本，相似度: {similarity:.3f}")
                
                # 步骤2：阈值判断
                explanation['process'].append("步骤2: 阈值判断")
                
                if similarity >= self.config.SIMILARITY_THRESHOLD:
                    explanation['decision'] = 'vector_retrieval'
                    explanation['final_prediction'] = best_match['label']
                    explanation['confidence'] = similarity
                    explanation['process'].append(f"相似度 {similarity:.3f} >= 阈值 {self.config.SIMILARITY_THRESHOLD}")
                    explanation['process'].append("决策: 使用向量检索结果")
                else:
                    explanation['process'].append(f"相似度 {similarity:.3f} < 阈值 {self.config.SIMILARITY_THRESHOLD}")
                    explanation['process'].append("步骤3: 模型分类")
                    
                    try:
                        predicted_label, model_confidence = model_trainer.predict_single(query)
                        
                        explanation['model_classification'] = {
                            'predicted_label': predicted_label,
                            'confidence': model_confidence
                        }
                        
                        explanation['decision'] = 'model_classification'
                        explanation['final_prediction'] = predicted_label
                        explanation['confidence'] = model_confidence
                        explanation['process'].append(f"模型预测: {predicted_label}, 置信度: {model_confidence:.3f}")
                        explanation['process'].append("决策: 使用LoRA微调模型结果")
                        
                    except Exception as e:
                        explanation['process'].append(f"模型分类失败: {str(e)}")
                        explanation['process'].append("回退到向量检索结果")
                        explanation['decision'] = 'vector_retrieval_fallback'
                        explanation['final_prediction'] = best_match['label']
                        explanation['confidence'] = similarity
            else:
                explanation['process'].append("向量检索失败，无法找到相似样本")
                explanation['decision'] = 'failed'
                explanation['final_prediction'] = None
                explanation['confidence'] = 0.0
            
            return explanation
            
        except Exception as e:
            explanation['error'] = str(e)
            explanation['process'].append(f"预测过程出错: {str(e)}")
            return explanation
