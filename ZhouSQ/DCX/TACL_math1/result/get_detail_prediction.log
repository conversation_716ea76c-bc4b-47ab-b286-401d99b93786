--- Log Start: 2025-09-03 10:05:44 ---
🎯 多知识点题目预测测试工具
================================================================================
📁 加载数据文件: /home/<USER>/ZhouSQ/DCX/TACL_math1/DCL/dataset/WebOfScience/wos_test.json
✅ 成功加载 6602 条数据

🚀 初始化分类器...
🔧 设置随机种子...
🔧 加载processor...
------------ using seed 171 ------------
------------ loading few-shot for 30 shot ------------
load index from path /home/<USER>/ZhouSQ/DCX/TACL_math1/DCL/dataset/WebOfScience/few-shot/seed_171-shot_30.json
------------ length few-shot: 31378 ------------
length dataset['train']: 31378
🔧 加载PLM和tokenizer...
🔧 加载模板...
✅ 找到模板文件: /home/<USER>/ZhouSQ/DCX/TACL_math1/template/wos_mask_template.txt
✅ 模板加载完成
🔧 创建verbalizer列表...
🔧 构建prompt模型...
using label emb for soft verbalizer
depth 0: torch.Size([1, 768])
depth 1: torch.Size([5, 768])
depth 2: torch.Size([30, 768])
depth 3: torch.Size([219, 768])
depth 4: torch.Size([904, 768])
depth 5: torch.Size([3732, 768])
depth 6: torch.Size([9578, 768])
depth 7: torch.Size([16007, 768])
depth 8: torch.Size([16056, 768])
🔧 移动模型到GPU...
🔧 加载训练好的权重...
✅ 启用了DataParallel，使用4张GPU进行推理
🔧 加载训练集嵌入...
✅ 模型初始化完成！支持9层分层分类
📊 各层标签数量: [1, 5, 30, 219, 904, 3732, 9578, 16007, 16056]
✅ 分类器初始化成功

🔍 批量测试多知识点题目预测效果（采样 6602 条）...
📊 多知识点题目统计:
   - 多知识点题目数量: 6597
   - 总知识点关联数: 6602
🚀 开始批量预测...
🔄 开始批量预测 6597 条文本（批大小: 256）...
/home/<USER>/ZhouSQ/DCX/TACL_math1/DCL/util/data_loader.py:131: UserWarning: Creating a tensor from a list of numpy.ndarrays is extremely slow. Please consider converting the list to a single numpy.ndarray with numpy.array() before converting to a tensor. (Triggered internally at /pytorch/torch/csrc/utils/tensor_new.cpp:254.)
  all_input_ids = torch.tensor([convert_tensor_to_numpy(f['input_ids']) for f in self.tensor_dataset],
✅ 批量预测完成！
✅ 批量预测完成！
🔍 分析预测结果...
✅ 结果分析完成！

📊 预测结果分析:
============================================================
📈 总体统计:
   - 测试题目数量: 6597
   - 成功预测数量: 6597
   - 预测失败数量: 0

🎯 匹配效果:
   - 平均匹配率: 63.20%
   - 完全匹配题目: 4169
   - 部分匹配题目: 0
   - 无匹配题目: 2428

📚 知识点数量分析:
   - 平均知识点数量: 1.00
   - 最多知识点数量: 2
   - 最少知识点数量: 1

📊 按知识点数量的匹配率（含细粒度TopK命中分析）:

🧩 题目含 1 个知识点（共 6592 题）：
   - 命中 1 个知识点: 4165题，占比 63.18%
   - 未命中任何知识点: 2427题，占比 36.82%

🧩 题目含 2 个知识点（共 5 题）：
   - 命中 1 个知识点: 0题，占比 0.00%
   - 命中 2 个知识点: 4题，占比 80.00%
   - 未命中任何知识点: 1题，占比 20.00%
✅ 结果已保存到: /home/<USER>/ZhouSQ/DCX/TACL_math1/result/multi_knowledge_prediction_results_old_test.json

🏁 测试完成！
--- Log Start: 2025-09-03 10:16:26 ---
🎯 多知识点题目预测测试工具
================================================================================
📁 加载数据文件: /home/<USER>/ZhouSQ/DCX/TACL_math1/DCL/dataset/WebOfScience/wos_test.json
✅ 成功加载 6602 条数据

🚀 初始化分类器...
🔧 设置随机种子...
🔧 加载processor...
------------ using seed 171 ------------
------------ loading few-shot for 30 shot ------------
load index from path /home/<USER>/ZhouSQ/DCX/TACL_math1/DCL/dataset/WebOfScience/few-shot/seed_171-shot_30.json
------------ length few-shot: 31378 ------------
length dataset['train']: 31378
🔧 加载PLM和tokenizer...
🔧 加载模板...
✅ 找到模板文件: /home/<USER>/ZhouSQ/DCX/TACL_math1/template/wos_mask_template.txt
✅ 模板加载完成
🔧 创建verbalizer列表...
🔧 构建prompt模型...
using label emb for soft verbalizer
depth 0: torch.Size([1, 768])
depth 1: torch.Size([5, 768])
depth 2: torch.Size([30, 768])
depth 3: torch.Size([219, 768])
depth 4: torch.Size([904, 768])
depth 5: torch.Size([3732, 768])
depth 6: torch.Size([9578, 768])
depth 7: torch.Size([16007, 768])
depth 8: torch.Size([16056, 768])
🔧 移动模型到GPU...
🔧 加载训练好的权重...
🔧 加载训练集嵌入...
✅ 模型初始化完成！支持9层分层分类
📊 各层标签数量: [1, 5, 30, 219, 904, 3732, 9578, 16007, 16056]
✅ 分类器初始化成功

🔍 批量测试多知识点题目预测效果（采样 6602 条）...
📊 多知识点题目统计:
   - 多知识点题目数量: 6597
   - 总知识点关联数: 6602
🚀 开始批量预测...
🔄 开始批量预测 6597 条文本（批大小: 256）...
/home/<USER>/ZhouSQ/DCX/TACL_math1/DCL/util/data_loader.py:131: UserWarning: Creating a tensor from a list of numpy.ndarrays is extremely slow. Please consider converting the list to a single numpy.ndarray with numpy.array() before converting to a tensor. (Triggered internally at /pytorch/torch/csrc/utils/tensor_new.cpp:254.)
  all_input_ids = torch.tensor([convert_tensor_to_numpy(f['input_ids']) for f in self.tensor_dataset],
✅ 批量预测完成！
✅ 批量预测完成！
🔍 分析预测结果...
✅ 结果分析完成！

📊 预测结果分析:
============================================================
📈 总体统计:
   - 测试题目数量: 6597
   - 成功预测数量: 6597
   - 预测失败数量: 0

🎯 匹配效果:
   - 平均匹配率: 72.29%
   - 完全匹配题目: 4769
   - 部分匹配题目: 0
   - 无匹配题目: 1828

📚 知识点数量分析:
   - 平均知识点数量: 1.00
   - 最多知识点数量: 2
   - 最少知识点数量: 1

📊 按知识点数量的匹配率（含细粒度TopK命中分析）:

🧩 题目含 1 个知识点（共 6592 题）：
   - 命中 1 个知识点: 4764题，占比 72.27%
   - 未命中任何知识点: 1828题，占比 27.73%

🧩 题目含 2 个知识点（共 5 题）：
   - 命中 1 个知识点: 0题，占比 0.00%
   - 命中 2 个知识点: 5题，占比 100.00%
✅ 结果已保存到: /home/<USER>/ZhouSQ/DCX/TACL_math1/result/multi_knowledge_prediction_results_old_test-1.json

🏁 测试完成！
--- Log Start: 2025-09-03 10:48:48 ---
🎯 多知识点题目预测测试工具
================================================================================
📁 加载数据文件: /home/<USER>/ZhouSQ/DCX/TACL_math1/DCL/dataset/WebOfScience/wos_test.json
✅ 成功加载 6602 条数据

🚀 初始化分类器...
🔧 设置随机种子...
🔧 加载processor...
------------ using seed 171 ------------
------------ loading few-shot for 30 shot ------------
load index from path /home/<USER>/ZhouSQ/DCX/TACL_math1/DCL/dataset/WebOfScience/few-shot/seed_171-shot_30.json
------------ length few-shot: 31378 ------------
length dataset['train']: 31378
🔧 加载PLM和tokenizer...
🔧 加载模板...
✅ 找到模板文件: /home/<USER>/ZhouSQ/DCX/TACL_math1/template/wos_mask_template.txt
✅ 模板加载完成
🔧 创建verbalizer列表...
🔧 构建prompt模型...
using label emb for soft verbalizer
depth 0: torch.Size([1, 768])
depth 1: torch.Size([5, 768])
depth 2: torch.Size([30, 768])
depth 3: torch.Size([219, 768])
depth 4: torch.Size([904, 768])
depth 5: torch.Size([3732, 768])
depth 6: torch.Size([9578, 768])
depth 7: torch.Size([16007, 768])
depth 8: torch.Size([16056, 768])
🔧 移动模型到GPU...
🔧 加载训练好的权重...
🔧 加载训练集嵌入...
✅ 模型初始化完成！支持9层分层分类
📊 各层标签数量: [1, 5, 30, 219, 904, 3732, 9578, 16007, 16056]
✅ 分类器初始化成功

🔍 批量测试多知识点题目预测效果（采样 6602 条）...
📊 多知识点题目统计:
   - 多知识点题目数量: 6597
   - 总知识点关联数: 6602
🚀 开始批量预测...
🔄 开始批量预测 6597 条文本（批大小: 256）...
/home/<USER>/ZhouSQ/DCX/TACL_math1/DCL/util/data_loader.py:131: UserWarning: Creating a tensor from a list of numpy.ndarrays is extremely slow. Please consider converting the list to a single numpy.ndarray with numpy.array() before converting to a tensor. (Triggered internally at /pytorch/torch/csrc/utils/tensor_new.cpp:254.)
  all_input_ids = torch.tensor([convert_tensor_to_numpy(f['input_ids']) for f in self.tensor_dataset],
✅ 批量预测完成！
✅ 批量预测完成！
🔍 分析预测结果...
✅ 结果分析完成！

📊 预测结果分析:
============================================================
📈 总体统计:
   - 测试题目数量: 6597
   - 成功预测数量: 6597
   - 预测失败数量: 0

🎯 匹配效果:
   - 平均匹配率: 72.68%
   - 完全匹配题目: 4795
   - 部分匹配题目: 0
   - 无匹配题目: 1802

📚 知识点数量分析:
   - 平均知识点数量: 1.00
   - 最多知识点数量: 2
   - 最少知识点数量: 1

📊 按知识点数量的匹配率（含细粒度TopK命中分析）:

🧩 题目含 1 个知识点（共 6592 题）：
   - 命中 1 个知识点: 4790题，占比 72.66%
   - 未命中任何知识点: 1802题，占比 27.34%

🧩 题目含 2 个知识点（共 5 题）：
   - 命中 1 个知识点: 0题，占比 0.00%
   - 命中 2 个知识点: 5题，占比 100.00%
✅ 结果已保存到: /home/<USER>/ZhouSQ/DCX/TACL_math1/result/multi_knowledge_prediction_results_old_test-2.json

🏁 测试完成！
--- Log Start: 2025-09-03 11:27:18 ---
🎯 多知识点题目预测测试工具
================================================================================
📁 加载数据文件: /home/<USER>/ZhouSQ/DCX/TACL_math1/DCL/dataset/WebOfScience/wos_test.json
✅ 成功加载 6602 条数据

🚀 初始化分类器...
🔧 设置随机种子...
🔧 加载processor...
------------ using seed 171 ------------
------------ loading few-shot for 30 shot ------------
load index from path /home/<USER>/ZhouSQ/DCX/TACL_math1/DCL/dataset/WebOfScience/few-shot/seed_171-shot_30.json
------------ length few-shot: 31378 ------------
length dataset['train']: 31378
🔧 加载PLM和tokenizer...
🔧 加载模板...
✅ 找到模板文件: /home/<USER>/ZhouSQ/DCX/TACL_math1/template/wos_mask_template.txt
✅ 模板加载完成
🔧 创建verbalizer列表...
🔧 构建prompt模型...
using label emb for soft verbalizer
depth 0: torch.Size([1, 768])
depth 1: torch.Size([5, 768])
depth 2: torch.Size([30, 768])
depth 3: torch.Size([219, 768])
depth 4: torch.Size([904, 768])
depth 5: torch.Size([3732, 768])
depth 6: torch.Size([9578, 768])
depth 7: torch.Size([16007, 768])
depth 8: torch.Size([16056, 768])
🔧 移动模型到GPU...
🔧 加载训练好的权重...
🔧 加载训练集嵌入...
✅ 模型初始化完成！支持9层分层分类
📊 各层标签数量: [1, 5, 30, 219, 904, 3732, 9578, 16007, 16056]
✅ 分类器初始化成功

🔍 批量测试多知识点题目预测效果（采样 6602 条）...
📊 多知识点题目统计:
   - 多知识点题目数量: 6597
   - 总知识点关联数: 6602
🚀 开始批量预测...
🔄 开始批量预测 6597 条文本（批大小: 256）...
/home/<USER>/ZhouSQ/DCX/TACL_math1/DCL/util/data_loader.py:131: UserWarning: Creating a tensor from a list of numpy.ndarrays is extremely slow. Please consider converting the list to a single numpy.ndarray with numpy.array() before converting to a tensor. (Triggered internally at /pytorch/torch/csrc/utils/tensor_new.cpp:254.)
  all_input_ids = torch.tensor([convert_tensor_to_numpy(f['input_ids']) for f in self.tensor_dataset],
✅ 批量预测完成！
✅ 批量预测完成！
🔍 分析预测结果...
✅ 结果分析完成！

📊 预测结果分析:
============================================================
📈 总体统计:
   - 测试题目数量: 6597
   - 成功预测数量: 6597
   - 预测失败数量: 0

🎯 匹配效果:
   - 平均匹配率: 72.68%
   - 完全匹配题目: 4795
   - 部分匹配题目: 0
   - 无匹配题目: 1802

📚 知识点数量分析:
   - 平均知识点数量: 1.00
   - 最多知识点数量: 2
   - 最少知识点数量: 1

📊 按知识点数量的匹配率（含细粒度TopK命中分析）:

🧩 题目含 1 个知识点（共 6592 题）：
   - 命中 1 个知识点: 4790题，占比 72.66%
   - 未命中任何知识点: 1802题，占比 27.34%

🧩 题目含 2 个知识点（共 5 题）：
   - 命中 1 个知识点: 0题，占比 0.00%
   - 命中 2 个知识点: 5题，占比 100.00%
✅ 结果已保存到: /home/<USER>/ZhouSQ/DCX/TACL_math1/result/multi_knowledge_prediction_results_old_test-3.json

🏁 测试完成！
--- Log Start: 2025-09-03 11:39:56 ---
🎯 多知识点题目预测测试工具
================================================================================
📁 加载数据文件: /home/<USER>/ZhouSQ/DCX/TACL_math1/DCL/dataset/WebOfScience/wos_test.json
✅ 成功加载 6602 条数据

🚀 初始化分类器...
🔧 设置随机种子...
🔧 加载processor...
------------ using seed 171 ------------
------------ loading few-shot for 30 shot ------------
load index from path /home/<USER>/ZhouSQ/DCX/TACL_math1/DCL/dataset/WebOfScience/few-shot/seed_171-shot_30.json
------------ length few-shot: 31378 ------------
length dataset['train']: 31378
🔧 加载PLM和tokenizer...
🔧 加载模板...
✅ 找到模板文件: /home/<USER>/ZhouSQ/DCX/TACL_math1/template/wos_mask_template.txt
✅ 模板加载完成
🔧 创建verbalizer列表...
🔧 构建prompt模型...
using label emb for soft verbalizer
depth 0: torch.Size([1, 768])
depth 1: torch.Size([5, 768])
depth 2: torch.Size([30, 768])
depth 3: torch.Size([219, 768])
depth 4: torch.Size([904, 768])
depth 5: torch.Size([3732, 768])
depth 6: torch.Size([9578, 768])
depth 7: torch.Size([16007, 768])
depth 8: torch.Size([16056, 768])
🔧 移动模型到GPU...
🔧 加载训练好的权重...
🔧 加载训练集嵌入...
✅ 模型初始化完成！支持9层分层分类
📊 各层标签数量: [1, 5, 30, 219, 904, 3732, 9578, 16007, 16056]
✅ 分类器初始化成功

🔍 批量测试多知识点题目预测效果（采样 6602 条）...
📊 多知识点题目统计:
   - 多知识点题目数量: 6597
   - 总知识点关联数: 6602
🚀 开始批量预测...
🔄 开始批量预测 6597 条文本（批大小: 256）...
/home/<USER>/ZhouSQ/DCX/TACL_math1/DCL/util/data_loader.py:131: UserWarning: Creating a tensor from a list of numpy.ndarrays is extremely slow. Please consider converting the list to a single numpy.ndarray with numpy.array() before converting to a tensor. (Triggered internally at /pytorch/torch/csrc/utils/tensor_new.cpp:254.)
  all_input_ids = torch.tensor([convert_tensor_to_numpy(f['input_ids']) for f in self.tensor_dataset],
✅ 批量预测完成！
✅ 批量预测完成！
🔍 分析预测结果...
✅ 结果分析完成！

📊 预测结果分析:
============================================================
📈 总体统计:
   - 测试题目数量: 6597
   - 成功预测数量: 6597
   - 预测失败数量: 0

🎯 匹配效果:
   - 平均匹配率: 82.58%
   - 完全匹配题目: 5448
   - 部分匹配题目: 0
   - 无匹配题目: 1149

📚 知识点数量分析:
   - 平均知识点数量: 1.00
   - 最多知识点数量: 2
   - 最少知识点数量: 1

📊 按知识点数量的匹配率（含细粒度TopK命中分析）:

🧩 题目含 1 个知识点（共 6592 题）：
   - 命中 1 个知识点: 5443题，占比 82.57%
   - 未命中任何知识点: 1149题，占比 17.43%

🧩 题目含 2 个知识点（共 5 题）：
   - 命中 1 个知识点: 0题，占比 0.00%
   - 命中 2 个知识点: 5题，占比 100.00%
✅ 结果已保存到: /home/<USER>/ZhouSQ/DCX/TACL_math1/result/multi_knowledge_prediction_results_old_test-4.json

🏁 测试完成！
