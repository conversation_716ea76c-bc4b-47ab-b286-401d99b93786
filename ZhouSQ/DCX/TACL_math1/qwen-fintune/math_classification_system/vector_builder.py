"""
向量构建模块 - 负责构建和管理向量知识库
"""

import json
import pickle
import numpy as np
import torch
import faiss
from typing import List, Dict, Tuple, Optional
from pathlib import Path
from tqdm import tqdm
from transformers import AutoTokenizer, AutoModel
import logging

logger = logging.getLogger(__name__)

class VectorBuilder:
    """向量构建器 - 负责文本向量化和相似度检索"""
    
    def __init__(self, config):
        self.config = config
        self.device = torch.device(config.DEVICE)
        
        # 模型组件
        self.tokenizer = None
        self.model = None
        
        # 数据存储
        self.texts = []
        self.labels = []
        self.vectors = None
        self.faiss_index = None
        
        # 缓存路径
        self.cache_path = config.CACHE_DIR / "vectors.pkl"

        # 检查是否存在旧版本的缓存
        self.legacy_cache_path = config.CACHE_DIR / "train_embeddings.pkl"
    
    def _load_embedding_model(self):
        """加载嵌入模型"""
        if self.model is not None:
            return
        
        try:
            logger.info(f"加载嵌入模型: {self.config.EMBEDDING_MODEL_PATH}")
            self.tokenizer = AutoTokenizer.from_pretrained(
                self.config.EMBEDDING_MODEL_PATH,
                trust_remote_code=True
            )
            self.model = AutoModel.from_pretrained(
                self.config.EMBEDDING_MODEL_PATH,
                trust_remote_code=True
            )
            self.model.to(self.device)
            self.model.eval()
            logger.info("嵌入模型加载完成")
        except Exception as e:
            logger.error(f"加载嵌入模型失败: {e}")
            raise
    
    def _load_training_data(self) -> Tuple[List[str], List[List[str]]]:
        """加载训练数据"""
        try:
            with open(self.config.TRAIN_DATA_PATH, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            texts = []
            labels = []
            for item in data:
                if 'doc_token' in item and 'doc_label' in item:
                    texts.append(item['doc_token'])
                    labels.append(item['doc_label'])
            
            logger.info(f"加载训练数据: {len(texts)} 条")
            return texts, labels
        except Exception as e:
            logger.error(f"加载训练数据失败: {e}")
            raise
    
    def _encode_texts(self, texts: List[str], show_progress: bool = True) -> np.ndarray:
        """将文本编码为向量"""
        self._load_embedding_model()
        
        all_embeddings = []
        batch_size = self.config.EMBEDDING_BATCH_SIZE
        
        iterator = range(0, len(texts), batch_size)
        if show_progress:
            iterator = tqdm(iterator, desc="编码文本", leave=False)
        
        with torch.no_grad():
            for i in iterator:
                batch_texts = texts[i:i + batch_size]
                
                # 分词
                inputs = self.tokenizer(
                    batch_texts,
                    padding=True,
                    truncation=True,
                    max_length=self.config.MAX_LENGTH,
                    return_tensors="pt"
                ).to(self.device)
                
                # 获取嵌入
                outputs = self.model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
                
                # L2归一化
                embeddings = torch.nn.functional.normalize(embeddings, p=2, dim=1)
                all_embeddings.append(embeddings.cpu().numpy())
        
        return np.vstack(all_embeddings)
    
    def _build_faiss_index(self):
        """构建FAISS索引"""
        if self.vectors is None:
            raise ValueError("向量未构建，无法创建索引")
        
        dimension = self.vectors.shape[1]
        self.faiss_index = faiss.IndexFlatIP(dimension)  # 内积搜索
        self.faiss_index.add(self.vectors.astype('float32'))
        
        logger.info(f"FAISS索引构建完成，维度: {dimension}, 样本数: {self.faiss_index.ntotal}")
    
    def _save_cache(self):
        """保存缓存"""
        try:
            cache_data = {
                'texts': self.texts,
                'labels': self.labels,
                'vectors': self.vectors,
                'config_hash': self._get_config_hash()
            }
            
            with open(self.cache_path, 'wb') as f:
                pickle.dump(cache_data, f)
            
            logger.info(f"向量缓存已保存: {self.cache_path}")
        except Exception as e:
            logger.error(f"保存缓存失败: {e}")
    
    def _load_cache(self) -> bool:
        """加载缓存 - 支持新旧版本的缓存文件"""
        cache_paths = [self.cache_path, self.legacy_cache_path]

        for cache_path in cache_paths:
            if not cache_path.exists():
                continue

            try:
                logger.info(f"尝试加载缓存: {cache_path}")
                with open(cache_path, 'rb') as f:
                    cache_data = pickle.load(f)

                # 检查缓存数据结构
                if 'texts' in cache_data and 'labels' in cache_data and 'vectors' in cache_data:
                    # 新版本缓存格式
                    self.texts = cache_data['texts']
                    self.labels = cache_data['labels']
                    self.vectors = cache_data['vectors']
                elif 'embeddings' in cache_data and 'texts' in cache_data:
                    # 旧版本缓存格式 (从similarity_matching_project)
                    logger.info("检测到旧版本缓存格式，正在转换...")
                    self.texts = cache_data['texts']
                    self.vectors = cache_data['embeddings']

                    # 需要重新加载标签数据
                    logger.info("重新加载标签数据...")
                    _, labels = self._load_training_data()
                    if len(labels) == len(self.texts):
                        self.labels = labels
                    else:
                        logger.warning("标签数据长度不匹配，缓存可能无效")
                        continue
                else:
                    logger.warning(f"未知的缓存格式: {cache_path}")
                    continue

                logger.info(f"成功从缓存加载向量: {len(self.texts)} 条")

                # 构建FAISS索引
                logger.info("构建FAISS索引...")
                self._build_faiss_index()

                # 如果使用的是旧版本缓存，保存为新格式
                if cache_path == self.legacy_cache_path:
                    logger.info("将旧版本缓存转换为新格式...")
                    self._save_cache()

                return True

            except Exception as e:
                logger.error(f"加载缓存失败 {cache_path}: {e}")
                continue

        return False
    
    def _get_config_hash(self) -> str:
        """获取配置哈希"""
        import hashlib
        config_str = f"{self.config.EMBEDDING_MODEL_PATH}_{self.config.MAX_LENGTH}_{self.config.EMBEDDING_BATCH_SIZE}"
        return hashlib.md5(config_str.encode()).hexdigest()
    
    def build_vectors(self, use_cache: bool = True, force_rebuild: bool = False) -> bool:
        """
        构建向量知识库
        
        Args:
            use_cache: 是否使用缓存
            force_rebuild: 是否强制重建
            
        Returns:
            bool: 是否成功构建
        """
        try:
            # 尝试从缓存加载
            if use_cache and not force_rebuild and self._load_cache():
                self._build_faiss_index()
                return True
            
            # 重新构建
            logger.info("构建向量知识库...")
            
            # 加载训练数据
            self.texts, self.labels = self._load_training_data()
            
            # 编码文本
            self.vectors = self._encode_texts(self.texts)
            
            # 构建FAISS索引
            self._build_faiss_index()
            
            # 保存缓存
            if use_cache:
                self._save_cache()
            
            logger.info("向量知识库构建完成")
            return True
            
        except Exception as e:
            logger.error(f"构建向量知识库失败: {e}")
            return False
    
    def find_similar(self, query: str, top_k: int = None) -> List[Dict]:
        """
        查找相似样本
        
        Args:
            query: 查询文本
            top_k: 返回最相似的K个样本
            
        Returns:
            List[Dict]: 相似样本列表
        """
        if self.faiss_index is None:
            raise ValueError("向量索引未构建，请先调用build_vectors()")
        
        top_k = top_k or self.config.TOP_K_SIMILAR
        
        # 编码查询文本
        query_vector = self._encode_texts([query], show_progress=False)
        
        # 搜索相似样本
        similarities, indices = self.faiss_index.search(
            query_vector.astype('float32'), 
            top_k
        )
        
        results = []
        for i, (similarity, idx) in enumerate(zip(similarities[0], indices[0])):
            if idx < len(self.texts):
                results.append({
                    'rank': i + 1,
                    'similarity': float(similarity),
                    'text': self.texts[idx],
                    'label': self.labels[idx],
                    'index': int(idx)
                })
        
        return results
