"""
统一配置文件 - 管理所有系统参数
"""

import os
from pathlib import Path

# =============================================================================
# 路径配置
# =============================================================================

# 项目根目录
PROJECT_ROOT = Path(__file__).parent
DATA_ROOT = "/home/<USER>/ZhouSQ/DCX/TACL_math1"

# 模型路径
EMBEDDING_MODEL_PATH = f"{DATA_ROOT}/Qwen3-Embedding-4B"
CLASSIFICATION_MODEL_PATH = f"{DATA_ROOT}/Qwen3-4B"
QWEN_MODEL_PATH = f"{DATA_ROOT}/Qwen3-4B"  # 兼容性别名

# 数据路径
DATA_DIR = f"{DATA_ROOT}/qwen-fintune/data"
TRAIN_DATA_PATH = f"{DATA_DIR}/train.json"
TEST_DATA_PATH = f"{DATA_DIR}/test.json"
VAL_DATA_PATH = f"{DATA_DIR}/val.json"

# 输出路径
OUTPUT_DIR = PROJECT_ROOT / "outputs"
EMBEDDINGS_CACHE_DIR = OUTPUT_DIR / "embeddings_cache"
RESULTS_DIR = OUTPUT_DIR / "results"
MODELS_DIR = OUTPUT_DIR / "models"
LOGS_DIR = OUTPUT_DIR / "logs"

# =============================================================================
# 系统配置
# =============================================================================

# 设备配置
DEVICE = "cuda"
GPU_MEMORY_FRACTION = 0.95  # GPU内存使用比例
USE_MULTI_GPU = True  # 是否使用多GPU训练
AUTO_DEVICE_MAP = True  # 是否自动分配设备映射
NUM_GPUS = 4  # GPU数量

# 并发配置
MAX_WORKERS = 4  # 最大工作线程数
BATCH_SIZE = 32  # 默认批处理大小

# =============================================================================
# 模型配置
# =============================================================================

# 嵌入模型配置
EMBEDDING_MAX_LENGTH = 768  # 嵌入模型最大序列长度
EMBEDDING_BATCH_SIZE = 32  # 嵌入计算批处理大小

# 分类模型配置
CLASSIFICATION_MAX_LENGTH = 768  # 分类模型最大序列长度

# =============================================================================
# 相似度匹配配置
# =============================================================================

SIMILARITY_THRESHOLD = 0.8  # 相似度阈值
TOP_K_SIMILAR = 5  # 返回最相似的K个样本
USE_FAISS = True  # 是否使用FAISS加速
FAISS_INDEX_TYPE = "IndexFlatIP"  # FAISS索引类型

# =============================================================================
# 混合策略配置
# =============================================================================

# 策略选择
DEFAULT_STRATEGY = "auto"  # 默认策略: similarity, classification, auto, ensemble
CLASSIFICATION_THRESHOLD = 0.8  # 分类模型置信度阈值
USE_CLASSIFICATION_FALLBACK = True  # 是否使用分类模型作为后备

# 集成策略权重
SIMILARITY_WEIGHT = 0.6  # 相似度匹配权重
CLASSIFICATION_WEIGHT = 0.4  # 分类模型权重

# =============================================================================
# 训练配置
# =============================================================================

# 基础训练参数
TRAIN_EPOCHS = 10
TRAIN_BATCH_SIZE = 8  # 多GPU时可以使用更大的批处理
EVAL_BATCH_SIZE = 16   # 多GPU时可以使用更大的批处理
GRADIENT_ACCUMULATION_STEPS = 1  # 多GPU时减少梯度累积
LEARNING_RATE = 2e-5
WARMUP_STEPS = 500
WEIGHT_DECAY = 0.01
MAX_GRAD_NORM = 1.0

# LoRA配置
USE_LORA = True  # 是否使用LoRA微调
LORA_R = 16  # LoRA rank
LORA_ALPHA = 32  # LoRA alpha
LORA_DROPOUT = 0.1  # LoRA dropout
LORA_TARGET_MODULES = [
    "q_proj", "v_proj", "k_proj", "o_proj",
    "gate_proj", "up_proj", "down_proj"
]

# 训练策略 - 统一使用epoch-based配置 (推荐)
# 优势: 更直观、更容易控制训练进度、与训练轮数一致
SAVE_STRATEGY = "epoch"  # 保存策略: "epoch" 或 "steps"
EVAL_STRATEGY = "epoch"  # 评估策略: "epoch" 或 "steps"

# epoch-based配置 (推荐)
SAVE_EPOCHS = 5  # 每2个epoch保存一次检查点
EVAL_EPOCHS = 1  # 每1个epoch评估一次
LOGGING_STEPS = 500  # 日志记录步数 (这个保持step-based，因为需要频繁记录)
SAVE_TOTAL_LIMIT = 3  # 保存检查点数量限制

# step-based配置 (备用，当SAVE_STRATEGY="steps"时使用)
SAVE_STEPS = 1000  # 保存检查点步数 (仅当strategy="steps"时使用)
EVAL_STEPS = 500   # 评估步数 (仅当strategy="steps"时使用)

# =============================================================================
# 评估配置
# =============================================================================

EVAL_BATCH_SIZE_LARGE = 32  # 大批量评估时的批处理大小
EVAL_STRATEGIES = ["similarity", "classification", "auto", "ensemble"]  # 评估策略列表

# =============================================================================
# 缓存配置
# =============================================================================

USE_CACHE = True  # 是否使用缓存
CACHE_EMBEDDINGS = True  # 是否缓存嵌入向量
CACHE_MODELS = True  # 是否缓存模型
AUTO_CLEAN_CACHE = False  # 是否自动清理缓存

# =============================================================================
# 日志配置
# =============================================================================

LOG_LEVEL = "INFO"  # 日志级别
LOG_FORMAT = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
LOG_FILE = LOGS_DIR / "system.log"

# =============================================================================
# 运行模式配置
# =============================================================================

# 可用的运行模式
AVAILABLE_MODES = [
    "info",           # 显示系统信息
    "setup",          # 环境检查和设置
    "build",          # 构建知识库
    "train",          # 训练分类模型
    "predict",        # 单次预测
    "batch",          # 批量预测
    "eval",           # 模型评估
    "interactive",    # 交互模式
    "add-data",       # 增量添加数据
    "export",         # 导出模型
    "clean",          # 清理缓存
]

# 默认运行模式
DEFAULT_MODE = "info"

# =============================================================================
# 初始化
# =============================================================================

def init_directories():
    """初始化所有必要的目录"""
    directories = [
        OUTPUT_DIR,
        EMBEDDINGS_CACHE_DIR,
        RESULTS_DIR,
        MODELS_DIR,
        LOGS_DIR,
    ]

    for directory in directories:
        directory.mkdir(parents=True, exist_ok=True)

def validate_config():
    """验证配置的有效性"""
    errors = []

    # 检查模型路径
    if not os.path.exists(EMBEDDING_MODEL_PATH):
        errors.append(f"嵌入模型路径不存在: {EMBEDDING_MODEL_PATH}")

    if not os.path.exists(CLASSIFICATION_MODEL_PATH):
        errors.append(f"分类模型路径不存在: {CLASSIFICATION_MODEL_PATH}")

    # 检查数据路径
    if not os.path.exists(TRAIN_DATA_PATH):
        errors.append(f"训练数据路径不存在: {TRAIN_DATA_PATH}")

    # 检查参数合理性
    if SIMILARITY_THRESHOLD < 0 or SIMILARITY_THRESHOLD > 1:
        errors.append("相似度阈值必须在0-1之间")

    if LORA_R <= 0:
        errors.append("LoRA rank必须大于0")

    return errors

# 自动初始化
init_directories()
