#!/usr/bin/env python3
"""
快速启动脚本 - 一键完成系统初始化和测试
"""

import sys
import time
from pathlib import Path

# 添加项目路径
sys.path.insert(0, str(Path(__file__).parent))

from math_classification_system import MathClassificationSystem, Config

def print_step(step_num, title, description=""):
    """打印步骤信息"""
    print(f"\n{'='*60}")
    print(f"🚀 步骤 {step_num}: {title}")
    if description:
        print(f"📝 {description}")
    print(f"{'='*60}")

def quick_start():
    """快速启动流程"""
    print("🧮 数学分类系统 - 快速启动")
    print("这个脚本将自动完成系统的初始化和测试")
    
    # 步骤1: 检查环境
    print_step(1, "环境检查", "检查模型路径和依赖")
    
    errors = Config.validate_paths()
    if errors:
        print("❌ 环境检查失败:")
        for error in errors:
            print(f"   • {error}")
        print("\n💡 请确保:")
        print("   1. 模型文件已正确下载")
        print("   2. config.py中的路径配置正确")
        print("   3. 训练数据文件存在")
        return False
    
    print("✅ 环境检查通过")
    print(f"   • 嵌入模型: {Config.EMBEDDING_MODEL_PATH}")
    print(f"   • 分类模型: {Config.CLASSIFICATION_MODEL_PATH}")
    print(f"   • 训练数据: {Config.TRAIN_DATA_PATH}")
    print(f"   • 相似度阈值: {Config.SIMILARITY_THRESHOLD}")
    
    # 步骤2: 初始化系统
    print_step(2, "系统初始化", "创建系统实例")
    
    try:
        system = MathClassificationSystem()
        print("✅ 系统实例创建成功")
    except Exception as e:
        print(f"❌ 系统初始化失败: {e}")
        return False
    
    # 步骤3: 构建向量知识库
    print_step(3, "构建向量知识库", "计算训练数据的向量表示")
    
    start_time = time.time()
    if system.build_vectors(use_cache=True):
        elapsed = time.time() - start_time
        print(f"✅ 向量知识库构建成功 (耗时: {elapsed:.1f}秒)")
    else:
        print("❌ 向量知识库构建失败")
        return False
    
    # 步骤4: 训练分类模型
    print_step(4, "训练分类模型", "使用LoRA微调技术训练模型")
    
    start_time = time.time()
    if system.train_model():
        elapsed = time.time() - start_time
        print(f"✅ 分类模型训练成功 (耗时: {elapsed:.1f}秒)")
    else:
        print("❌ 分类模型训练失败")
        return False
    
    # 步骤5: 系统测试
    print_step(5, "系统测试", "测试预测功能")
    
    test_queries = [
        "小明有10个苹果，给了小红3个，还剩几个？",
        "计算正方形的面积公式",
        "解方程：x + 5 = 12"
    ]
    
    print("🔍 测试查询:")
    all_success = True
    
    for i, query in enumerate(test_queries, 1):
        print(f"\n   测试 {i}: {query}")
        
        try:
            result = system.predict(query)
            
            if result['prediction']:
                print(f"   ✅ 预测: {' -> '.join(result['prediction'])}")
                print(f"      置信度: {result['confidence']:.3f}")
                print(f"      策略: {result['strategy_used']}")
            else:
                print(f"   ❌ 预测失败: {result.get('reasoning', '未知错误')}")
                all_success = False
                
        except Exception as e:
            print(f"   ❌ 预测出错: {e}")
            all_success = False
    
    if all_success:
        print("\n✅ 所有测试通过")
    else:
        print("\n⚠️  部分测试失败，但系统基本可用")
    
    # 步骤6: 使用指南
    print_step(6, "使用指南", "如何使用系统")
    
    print("🎯 系统已准备就绪！您可以:")
    print()
    print("1. 交互式预测:")
    print("   python main.py interactive")
    print()
    print("2. 单次预测:")
    print("   python main.py predict --query '你的问题'")
    print()
    print("3. 评估系统:")
    print("   python main.py evaluate")
    print()
    print("4. 查看系统状态:")
    print("   python main.py status")
    print()
    print("5. 运行完整示例:")
    print("   python example.py")
    print()
    print("💡 提示:")
    print(f"   • 当前阈值: {Config.SIMILARITY_THRESHOLD}")
    print("   • 高于阈值时使用向量检索（快速）")
    print("   • 低于阈值时使用LoRA模型（精确）")
    print("   • 可在 config.py 中调整阈值")
    
    return True

def main():
    """主函数"""
    try:
        success = quick_start()
        
        if success:
            print(f"\n{'='*60}")
            print("🎉 快速启动完成！系统已准备就绪")
            print(f"{'='*60}")
            
            # 询问是否进入交互模式
            response = input("\n是否立即进入交互式预测模式？(y/N): ").strip().lower()
            if response == 'y':
                print("\n🎮 启动交互式预测模式...")
                import subprocess
                subprocess.run([sys.executable, "main.py", "interactive"])
        else:
            print(f"\n{'='*60}")
            print("❌ 快速启动失败，请检查错误信息")
            print(f"{'='*60}")
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\n👋 快速启动被用户中断")
    except Exception as e:
        print(f"\n❌ 快速启动出错: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()
