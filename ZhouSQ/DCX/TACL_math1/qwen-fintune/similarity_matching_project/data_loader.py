"""
数据加载和预处理模块
"""

import json
import pandas as pd
from typing import List, Dict, Tuple
import config

class DataLoader:
    def __init__(self):
        self.train_data = None
        self.test_data = None
        self.val_data = None
    
    def load_json_data(self, file_path: str) -> List[Dict]:
        """加载JSON数据"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            print(f"成功加载 {len(data)} 条数据从 {file_path}")
            return data
        except Exception as e:
            print(f"加载数据失败: {e}")
            return []
    
    def load_all_data(self):
        """加载所有数据集"""
        self.train_data = self.load_json_data(config.TRAIN_DATA_PATH)
        self.test_data = self.load_json_data(config.TEST_DATA_PATH)
        self.val_data = self.load_json_data(config.VAL_DATA_PATH)
        
        return {
            'train': self.train_data,
            'test': self.test_data,
            'val': self.val_data
        }
    
    def get_doc_tokens_and_labels(self, data: List[Dict]) -> Tuple[List[str], List[List[str]]]:
        """提取doc_token和doc_label"""
        doc_tokens = []
        doc_labels = []
        
        for item in data:
            if 'doc_token' in item and 'doc_label' in item:
                doc_tokens.append(item['doc_token'])
                doc_labels.append(item['doc_label'])
        
        return doc_tokens, doc_labels
    
    def create_training_dataset(self) -> Tuple[List[str], List[List[str]]]:
        """创建训练数据集"""
        if self.train_data is None:
            self.load_all_data()
        
        return self.get_doc_tokens_and_labels(self.train_data)
    
    def create_test_dataset(self) -> Tuple[List[str], List[List[str]]]:
        """创建测试数据集"""
        if self.test_data is None:
            self.load_all_data()
        
        return self.get_doc_tokens_and_labels(self.test_data)
    
    def get_data_statistics(self):
        """获取数据统计信息"""
        if self.train_data is None:
            self.load_all_data()
        
        stats = {
            'train_size': len(self.train_data) if self.train_data else 0,
            'test_size': len(self.test_data) if self.test_data else 0,
            'val_size': len(self.val_data) if self.val_data else 0
        }
        
        # 分析标签层次结构
        if self.train_data:
            all_labels = []
            for item in self.train_data:
                if 'doc_label' in item:
                    all_labels.extend(item['doc_label'])
            
            unique_labels = list(set(all_labels))
            stats['unique_labels_count'] = len(unique_labels)
            stats['sample_labels'] = unique_labels[:10]  # 显示前10个标签作为示例
        
        return stats
