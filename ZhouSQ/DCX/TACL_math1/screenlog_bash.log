
[K(htc) [01;32<PERSON><PERSON>@8034a100[00m:[01;34m~/ZhouSQ/DCX/TACL_math1/qwen-fintune/similarity_matching_project[00m$ python run.py --mode eval --strategy auto [K[K[K[K[K[Kcd  [K/home/<USER>/ZhouSQ/DCX/TACL_math1/qwen-fintune/math_classification_system
[?2004l
[?2004h(htc) [01;32<PERSON>er@8034a100[00m:[01;34m~/ZhouSQ/DCX/TACL_math1/qwen-fintune/math_classification_system[00m$ cd /home/<USER>/ZhouSQ/DCX/TACL_math1/qwen-fintune
[?2004l
[?2004h(htc) [01;32muser@8034a100[00m:[01;34m~/ZhouSQ/DCX/TACL_math1/qwen-fintune[00m$ python main.py build
[?2004l
============================================================
🧮 数学分类系统 - 基于阈值的智能分类
============================================================
📊 相似度阈值: 0.8
🔧 设备: cuda
⚡ LoRA微调: 启用
============================================================
🔨 构建向量知识库...
14:26:41 - INFO - 数学分类系统初始化完成
14:26:41 - INFO - 开始构建向量知识库...
14:26:41 - INFO - 构建向量知识库...
14:26:41 - INFO - 加载训练数据: 48211 条
14:26:41 - INFO - 加载嵌入模型: /home/<USER>/ZhouSQ/DCX/TACL_math1/Qwen3-Embedding-4B

Loading checkpoint shards:   0%|                                                                                    | 0/2 [00:00<?, ?it/s]
Loading checkpoint shards:  50%|██████████████████████████████████████                                      | 1/2 [00:00<00:00,  1.69it/s]
Loading checkpoint shards: 100%|████████████████████████████████████████████████████████████████████████████| 2/2 [00:00<00:00,  2.15it/s]
Loading checkpoint shards: 100%|████████████████████████████████████████████████████████████████████████████| 2/2 [00:00<00:00,  2.06it/s]
14:26:48 - INFO - 嵌入模型加载完成

编码文本:   0%|                                                                                                  | 0/1507 [00:00<?, ?it/s]
编码文本:   0%|                                                                                        | 1/1507 [00:07<3:16:00,  7.81s/it]
编码文本:   0%|                                                                                        | 2/1507 [00:15<3:11:40,  7.64s/it]^C
                                                                                                                                          
Traceback (most recent call last):
  File "/home/<USER>/ZhouSQ/DCX/TACL_math1/qwen-fintune/main.py", line 300, in <module>
    main()
  File "/home/<USER>/ZhouSQ/DCX/TACL_math1/qwen-fintune/main.py", line 285, in main
    cmd_build(args)
  File "/home/<USER>/ZhouSQ/DCX/TACL_math1/qwen-fintune/main.py", line 49, in cmd_build
    success = system.build_vectors(
              ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/ZhouSQ/DCX/TACL_math1/qwen-fintune/math_classification_system/core_system.py", line 58, in build_vectors
    success = self.vector_builder.build_vectors(use_cache=use_cache, force_rebuild=force_rebuild)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/ZhouSQ/DCX/TACL_math1/qwen-fintune/math_classification_system/vector_builder.py", line 195, in build_vectors
    self.vectors = self._encode_texts(self.texts)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/ZhouSQ/DCX/TACL_math1/qwen-fintune/math_classification_system/vector_builder.py", line 104, in _encode_texts
    outputs = self.model(**inputs)
              ^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/htc/lib/python3.12/site-packages/torch/nn/modules/module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/htc/lib/python3.12/site-packages/torch/nn/modules/module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/htc/lib/python3.12/site-packages/transformers/utils/generic.py", line 1069, in wrapper
    outputs = func(self, *args, **kwargs)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/htc/lib/python3.12/site-packages/transformers/models/qwen3/modeling_qwen3.py", line 405, in forward
    hidden_states = decoder_layer(
                    ^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/htc/lib/python3.12/site-packages/transformers/modeling_layers.py", line 94, in __call__
    return super().__call__(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/htc/lib/python3.12/site-packages/torch/nn/modules/module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/htc/lib/python3.12/site-packages/torch/nn/modules/module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/htc/lib/python3.12/site-packages/transformers/models/qwen3/modeling_qwen3.py", line 257, in forward
    hidden_states, _ = self.self_attn(
                       ^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/htc/lib/python3.12/site-packages/torch/nn/modules/module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/htc/lib/python3.12/site-packages/torch/nn/modules/module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/htc/lib/python3.12/site-packages/transformers/models/qwen3/modeling_qwen3.py", line 214, in forward
    attn_output, attn_weights = attention_interface(
                                ^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/htc/lib/python3.12/site-packages/transformers/integrations/sdpa_attention.py", line 81, in sdpa_attention_forward
    attn_output = torch.nn.functional.scaled_dot_product_attention(
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
KeyboardInterrupt

[?2004h(htc) [01;32muser@8034a100[00m:[01;34m~/ZhouSQ/DCX/TACL_math1/qwen-fintune[00m$ python main.py buildcd /home/<USER>/ZhouSQ/DCX/TACL_math1/qwen-fintune/math_classification_system
[C[C[C[C[C[C[C[C[C[C[C[C[C[C[C[C[C[C[C[C[C[C[C[C[C[C[C[C[C[C[C[C[C[C[C[C[C[C[C[C[C[C[C[C[C[C[C[C[C[C[C[C[C[C[C[C[C[Cpython simple_eval.py [Kcd /home/<USER>/ZhouSQ/DCX/TACL_math1/qwen-fintune/math_classification_system[K[28Ppython main.py build[Kpython main.py build
[?2004l
============================================================
🧮 数学分类系统 - 基于阈值的智能分类
============================================================
📊 相似度阈值: 0.8
🔧 设备: cuda
⚡ LoRA微调: 启用
============================================================
🔨 构建向量知识库...
14:35:40 - INFO - 数学分类系统初始化完成
14:35:40 - INFO - 开始构建向量知识库...
14:35:40 - INFO - 构建向量知识库...
14:35:41 - INFO - 加载训练数据: 48211 条
14:35:41 - INFO - 加载嵌入模型: /home/<USER>/ZhouSQ/DCX/TACL_math1/Qwen3-Embedding-4B

Loading checkpoint shards:   0%|                                                                                    | 0/2 [00:00<?, ?it/s]
Loading checkpoint shards:  50%|██████████████████████████████████████                                      | 1/2 [00:00<00:00,  1.68it/s]
Loading checkpoint shards: 100%|████████████████████████████████████████████████████████████████████████████| 2/2 [00:00<00:00,  2.17it/s]
Loading checkpoint shards: 100%|████████████████████████████████████████████████████████████████████████████| 2/2 [00:00<00:00,  2.07it/s]
14:35:47 - INFO - 嵌入模型加载完成

编码文本:   0%|                                                                                                  | 0/1507 [00:00<?, ?it/s]
编码文本:   0%|                                                                                        | 1/1507 [00:07<3:15:04,  7.77s/it]^C
                                                                                                                                          
Traceback (most recent call last):
  File "/home/<USER>/ZhouSQ/DCX/TACL_math1/qwen-fintune/main.py", line 300, in <module>
    main()
  File "/home/<USER>/ZhouSQ/DCX/TACL_math1/qwen-fintune/main.py", line 285, in main
    cmd_build(args)
  File "/home/<USER>/ZhouSQ/DCX/TACL_math1/qwen-fintune/main.py", line 49, in cmd_build
    success = system.build_vectors(
              ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/ZhouSQ/DCX/TACL_math1/qwen-fintune/math_classification_system/core_system.py", line 58, in build_vectors
    success = self.vector_builder.build_vectors(use_cache=use_cache, force_rebuild=force_rebuild)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/ZhouSQ/DCX/TACL_math1/qwen-fintune/math_classification_system/vector_builder.py", line 195, in build_vectors
    self.vectors = self._encode_texts(self.texts)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/ZhouSQ/DCX/TACL_math1/qwen-fintune/math_classification_system/vector_builder.py", line 109, in _encode_texts
    all_embeddings.append(embeddings.cpu().numpy())
                          ^^^^^^^^^^^^^^^^
KeyboardInterrupt

[?2004h(htc) [01;32muser@8034a100[00m:[01;34m~/ZhouSQ/DCX/TACL_math1/qwen-fintune[00m$ 