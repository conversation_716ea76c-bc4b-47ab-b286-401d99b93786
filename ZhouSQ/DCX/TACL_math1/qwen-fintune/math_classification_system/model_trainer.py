"""
模型微调模块 - 负责LoRA微调和模型训练
"""

import json
import torch
import numpy as np
from typing import List, Dict, Tuple, Optional
from pathlib import Path
from torch.utils.data import Dataset
from transformers import (
    AutoTokenizer, AutoModelForSequenceClassification,
    Trainer, TrainingArguments, DataCollatorWithPadding
)
from sklearn.preprocessing import LabelEncoder
from sklearn.metrics import accuracy_score
import logging

# LoRA相关导入
try:
    from peft import LoraConfig, get_peft_model, TaskType, PeftModel
    PEFT_AVAILABLE = True
except ImportError:
    PEFT_AVAILABLE = False
    print("警告: peft库未安装，将使用全量微调")

logger = logging.getLogger(__name__)

class MathDataset(Dataset):
    """数学分类数据集"""
    
    def __init__(self, texts: List[str], labels: List[List[str]], tokenizer, max_length: int = 512):
        self.texts = texts
        self.labels = labels
        self.tokenizer = tokenizer
        self.max_length = max_length
        
        # 将层次标签转换为字符串
        self.label_strings = [" -> ".join(label) for label in labels]
        
        # 创建标签编码器
        self.label_encoder = LabelEncoder()
        self.encoded_labels = self.label_encoder.fit_transform(self.label_strings)
    
    def __len__(self):
        return len(self.texts)
    
    def __getitem__(self, idx):
        text = str(self.texts[idx])
        
        encoding = self.tokenizer(
            text,
            truncation=True,
            padding='max_length',
            max_length=self.max_length,
            return_tensors='pt'
        )
        
        return {
            'input_ids': encoding['input_ids'].flatten(),
            'attention_mask': encoding['attention_mask'].flatten(),
            'labels': torch.tensor(self.encoded_labels[idx], dtype=torch.long)
        }

class ModelTrainer:
    """模型训练器 - 负责LoRA微调"""
    
    def __init__(self, config):
        self.config = config
        self.device = torch.device(config.DEVICE)
        
        # 模型组件
        self.tokenizer = None
        self.model = None
        self.label_encoder = None
        
        # 训练组件
        self.trainer = None
        
        # 模型保存路径
        self.model_save_path = config.MODELS_DIR / "classification_model"
    
    def _load_training_data(self) -> Tuple[List[str], List[List[str]], List[str], List[List[str]]]:
        """加载训练和验证数据"""
        try:
            # 加载训练数据
            with open(self.config.TRAIN_DATA_PATH, 'r', encoding='utf-8') as f:
                train_data = json.load(f)
            
            train_texts = []
            train_labels = []
            for item in train_data:
                if 'doc_token' in item and 'doc_label' in item:
                    train_texts.append(item['doc_token'])
                    train_labels.append(item['doc_label'])
            
            # 尝试加载验证数据
            val_texts = []
            val_labels = []
            try:
                with open(self.config.VAL_DATA_PATH, 'r', encoding='utf-8') as f:
                    val_data = json.load(f)
                
                for item in val_data:
                    if 'doc_token' in item and 'doc_label' in item:
                        val_texts.append(item['doc_token'])
                        val_labels.append(item['doc_label'])
            except:
                logger.warning("未找到验证数据，从训练数据中分割20%作为验证集")
                split_idx = int(len(train_texts) * 0.8)
                val_texts = train_texts[split_idx:]
                val_labels = train_labels[split_idx:]
                train_texts = train_texts[:split_idx]
                train_labels = train_labels[:split_idx]
            
            logger.info(f"训练数据: {len(train_texts)} 条")
            logger.info(f"验证数据: {len(val_texts)} 条")
            
            return train_texts, train_labels, val_texts, val_labels
            
        except Exception as e:
            logger.error(f"加载训练数据失败: {e}")
            raise
    
    def _load_model(self, num_labels: int):
        """加载预训练模型"""
        try:
            logger.info(f"加载分类模型: {self.config.CLASSIFICATION_MODEL_PATH}")
            
            self.tokenizer = AutoTokenizer.from_pretrained(
                self.config.CLASSIFICATION_MODEL_PATH,
                trust_remote_code=True
            )
            
            # 添加pad_token如果不存在
            if self.tokenizer.pad_token is None:
                self.tokenizer.pad_token = self.tokenizer.eos_token
                self.tokenizer.pad_token_id = self.tokenizer.eos_token_id
            
            # 加载模型
            self.model = AutoModelForSequenceClassification.from_pretrained(
                self.config.CLASSIFICATION_MODEL_PATH,
                num_labels=num_labels,
                trust_remote_code=True,
                pad_token_id=self.tokenizer.pad_token_id,
            )
            
            # 应用LoRA配置
            if self.config.USE_LORA and PEFT_AVAILABLE:
                logger.info("应用LoRA配置...")
                lora_config = LoraConfig(
                    task_type=TaskType.SEQ_CLS,
                    r=self.config.LORA_R,
                    lora_alpha=self.config.LORA_ALPHA,
                    lora_dropout=self.config.LORA_DROPOUT,
                    target_modules=self.config.LORA_TARGET_MODULES,
                )
                self.model = get_peft_model(self.model, lora_config)
                self.model.print_trainable_parameters()
                logger.info("LoRA配置应用成功")
            else:
                if self.config.USE_LORA:
                    logger.warning("peft库不可用，使用全量微调")
                else:
                    logger.info("使用全量微调")
            
            self.model.to(self.device)
            logger.info("分类模型加载成功")
            
        except Exception as e:
            logger.error(f"加载分类模型失败: {e}")
            raise
    
    def _prepare_datasets(self, train_texts: List[str], train_labels: List[List[str]], 
                         val_texts: List[str], val_labels: List[List[str]]) -> Tuple[MathDataset, MathDataset]:
        """准备训练数据集"""
        train_dataset = MathDataset(train_texts, train_labels, self.tokenizer, self.config.MAX_LENGTH)
        val_dataset = MathDataset(val_texts, val_labels, self.tokenizer, self.config.MAX_LENGTH)
        
        # 保存标签编码器
        self.label_encoder = train_dataset.label_encoder
        
        return train_dataset, val_dataset
    
    def _compute_metrics(self, eval_pred):
        """计算评估指标"""
        predictions, labels = eval_pred
        predictions = np.argmax(predictions, axis=1)
        accuracy = accuracy_score(labels, predictions)
        return {'accuracy': accuracy}
    
    def _setup_trainer(self, train_dataset: MathDataset, val_dataset: MathDataset):
        """设置训练器"""
        training_args = TrainingArguments(
            output_dir=str(self.model_save_path),
            num_train_epochs=self.config.TRAIN_EPOCHS,
            per_device_train_batch_size=self.config.TRAIN_BATCH_SIZE,
            per_device_eval_batch_size=self.config.TRAIN_BATCH_SIZE,
            learning_rate=self.config.LEARNING_RATE,
            warmup_steps=100,
            weight_decay=0.01,
            logging_dir=str(self.model_save_path / 'logs'),
            logging_steps=100,
            eval_strategy="epoch",
            save_strategy="epoch",
            save_total_limit=2,
            load_best_model_at_end=True,
            metric_for_best_model="eval_accuracy",
            greater_is_better=True,
            report_to=None,  # 禁用wandb等
            remove_unused_columns=True,
        )
        
        data_collator = DataCollatorWithPadding(tokenizer=self.tokenizer)
        
        self.trainer = Trainer(
            model=self.model,
            args=training_args,
            train_dataset=train_dataset,
            eval_dataset=val_dataset,
            processing_class=self.tokenizer,
            data_collator=data_collator,
            compute_metrics=self._compute_metrics,
        )
    
    def _save_model(self):
        """保存模型和标签编码器"""
        try:
            # 保存模型
            self.trainer.save_model()
            self.tokenizer.save_pretrained(self.model_save_path)
            
            # 保存标签编码器
            label_encoder_path = self.model_save_path / "label_encoder.json"
            label_mapping = {
                'classes_': self.label_encoder.classes_.tolist()
            }
            with open(label_encoder_path, 'w', encoding='utf-8') as f:
                json.dump(label_mapping, f, ensure_ascii=False, indent=2)
            
            logger.info(f"模型已保存到: {self.model_save_path}")
            
        except Exception as e:
            logger.error(f"保存模型失败: {e}")
            raise
    
    def train_model(self, force_retrain: bool = False) -> bool:
        """
        训练分类模型
        
        Args:
            force_retrain: 是否强制重新训练
            
        Returns:
            bool: 是否成功训练
        """
        try:
            # 检查是否已有训练好的模型
            if not force_retrain and self.model_save_path.exists() and (self.model_save_path / "config.json").exists():
                logger.info("发现已训练的模型，正在加载...")
                return self.load_trained_model()
            
            logger.info("开始训练分类模型...")
            
            # 加载训练数据
            train_texts, train_labels, val_texts, val_labels = self._load_training_data()
            
            # 获取标签数量
            all_label_strings = [" -> ".join(label) for label in train_labels + val_labels]
            num_labels = len(set(all_label_strings))
            logger.info(f"标签数量: {num_labels}")
            
            # 加载模型
            self._load_model(num_labels)
            
            # 准备数据集
            train_dataset, val_dataset = self._prepare_datasets(train_texts, train_labels, val_texts, val_labels)
            
            # 设置训练器
            self._setup_trainer(train_dataset, val_dataset)
            
            # 开始训练
            logger.info("开始训练...")
            self.trainer.train()
            
            # 保存模型
            self._save_model()
            
            logger.info("模型训练完成")
            return True
            
        except Exception as e:
            logger.error(f"训练模型失败: {e}")
            return False

    def load_trained_model(self) -> bool:
        """加载已训练的模型"""
        try:
            logger.info(f"加载已训练的模型: {self.model_save_path}")

            self.tokenizer = AutoTokenizer.from_pretrained(
                str(self.model_save_path),
                trust_remote_code=True
            )

            # 检查是否是LoRA模型
            adapter_config_path = self.model_save_path / "adapter_config.json"
            if adapter_config_path.exists() and PEFT_AVAILABLE:
                logger.info("检测到LoRA模型，正在加载...")
                # 先加载基础模型
                base_model = AutoModelForSequenceClassification.from_pretrained(
                    self.config.CLASSIFICATION_MODEL_PATH,
                    trust_remote_code=True
                )
                # 加载LoRA适配器
                self.model = PeftModel.from_pretrained(base_model, str(self.model_save_path))
                logger.info("LoRA模型加载成功")
            else:
                # 加载全量微调模型
                self.model = AutoModelForSequenceClassification.from_pretrained(
                    str(self.model_save_path),
                    trust_remote_code=True
                )
                logger.info("全量微调模型加载成功")

            self.model.to(self.device)
            self.model.eval()

            # 加载标签编码器
            label_encoder_path = self.model_save_path / "label_encoder.json"
            with open(label_encoder_path, 'r', encoding='utf-8') as f:
                label_mapping = json.load(f)

            from sklearn.preprocessing import LabelEncoder
            self.label_encoder = LabelEncoder()
            self.label_encoder.classes_ = np.array(label_mapping['classes_'])

            logger.info("已训练模型加载成功")
            return True

        except Exception as e:
            logger.error(f"加载已训练模型失败: {e}")
            return False

    def predict(self, texts: List[str]) -> List[Tuple[List[str], float]]:
        """预测文本标签"""
        if self.model is None or self.label_encoder is None:
            raise ValueError("模型未加载，请先训练或加载模型")

        self.model.eval()
        predictions = []

        with torch.no_grad():
            for text in texts:
                # 编码文本
                encoding = self.tokenizer(
                    text,
                    truncation=True,
                    padding='max_length',
                    max_length=self.config.MAX_LENGTH,
                    return_tensors='pt'
                ).to(self.device)

                # 预测
                outputs = self.model(**encoding)
                logits = outputs.logits
                probabilities = torch.softmax(logits, dim=-1)

                # 获取最高概率的预测
                predicted_class_id = torch.argmax(probabilities, dim=-1).item()
                confidence = probabilities[0][predicted_class_id].item()

                # 解码标签
                predicted_label_string = self.label_encoder.inverse_transform([predicted_class_id])[0]
                predicted_label = predicted_label_string.split(" -> ")

                predictions.append((predicted_label, confidence))

        return predictions

    def predict_single(self, text: str) -> Tuple[List[str], float]:
        """预测单个文本"""
        results = self.predict([text])
        return results[0] if results else ([], 0.0)
