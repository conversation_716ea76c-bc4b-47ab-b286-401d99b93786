#!/usr/bin/env python3
"""
使用示例 - 展示如何使用数学分类系统
"""

import sys
from pathlib import Path

# 添加项目路径
sys.path.insert(0, str(Path(__file__).parent))

from math_classification_system import MathClassificationSystem, Config

def example_basic_usage():
    """基础使用示例"""
    print("=" * 60)
    print("📚 基础使用示例")
    print("=" * 60)
    
    # 创建系统实例
    system = MathClassificationSystem()
    
    # 检查系统状态
    print("1. 检查系统状态:")
    status = system.get_system_status()
    print(f"   向量知识库: {'✅' if status['vector_ready'] else '❌'}")
    print(f"   分类模型: {'✅' if status['model_ready'] else '❌'}")
    print(f"   相似度阈值: {status['similarity_threshold']}")
    
    # 如果系统未准备就绪，先初始化
    if not status['vector_ready']:
        print("\n2. 构建向量知识库...")
        if system.build_vectors():
            print("   ✅ 向量知识库构建成功")
        else:
            print("   ❌ 向量知识库构建失败")
            return
    
    if not status['model_ready']:
        print("\n3. 训练分类模型...")
        if system.train_model():
            print("   ✅ 分类模型训练成功")
        else:
            print("   ❌ 分类模型训练失败")
            return
    
    # 进行预测
    print("\n4. 预测示例:")
    test_queries = [
        "小明有5个苹果，吃了2个，还剩几个？",
        "一个正方形的边长是4cm，求它的面积",
        "解方程：2x + 3 = 7",
        "计算：(3 + 4) × 2 - 1"
    ]
    
    for i, query in enumerate(test_queries, 1):
        print(f"\n   查询 {i}: {query}")
        result = system.predict(query)
        
        if result['prediction']:
            print(f"   预测: {' -> '.join(result['prediction'])}")
            print(f"   置信度: {result['confidence']:.3f}")
            print(f"   策略: {result['strategy_used']}")
        else:
            print(f"   预测失败: {result.get('reasoning', '未知错误')}")

def example_threshold_comparison():
    """阈值对比示例"""
    print("\n" + "=" * 60)
    print("🎯 阈值对比示例")
    print("=" * 60)
    
    # 测试不同阈值的效果
    test_query = "计算三角形的面积公式是什么？"
    
    print(f"测试查询: {test_query}")
    print(f"当前阈值: {Config.SIMILARITY_THRESHOLD}")
    
    system = MathClassificationSystem()
    
    # 获取详细预测解释
    if system.vector_ready and system.model_ready:
        explanation = system.predictor.explain_prediction(
            test_query, system.vector_builder, system.model_trainer
        )
        
        print(f"\n📊 预测分析:")
        print(f"最终预测: {' -> '.join(explanation.get('final_prediction', [])) if explanation.get('final_prediction') else '无'}")
        print(f"置信度: {explanation.get('confidence', 0):.3f}")
        print(f"决策策略: {explanation.get('decision', '未知')}")
        
        print(f"\n🔍 详细过程:")
        for step in explanation.get('process', []):
            print(f"  • {step}")
        
        # 显示向量检索结果
        if 'vector_retrieval' in explanation:
            print(f"\n📋 向量检索结果:")
            for i, match in enumerate(explanation['vector_retrieval']['top_matches'][:3], 1):
                print(f"  {i}. 相似度: {match['similarity']:.3f}")
                print(f"     标签: {' -> '.join(match['label'])}")
                print(f"     文本: {match['text'][:100]}...")
        
        # 显示模型分类结果
        if 'model_classification' in explanation:
            print(f"\n🤖 模型分类结果:")
            model_result = explanation['model_classification']
            print(f"  预测: {' -> '.join(model_result['predicted_label'])}")
            print(f"  置信度: {model_result['confidence']:.3f}")

def example_batch_prediction():
    """批量预测示例"""
    print("\n" + "=" * 60)
    print("📦 批量预测示例")
    print("=" * 60)
    
    system = MathClassificationSystem()
    
    # 准备批量查询
    batch_queries = [
        "求解一元二次方程 x² - 5x + 6 = 0",
        "计算圆的周长公式",
        "什么是勾股定理？",
        "如何计算长方形的周长？",
        "分数 3/4 + 1/2 等于多少？"
    ]
    
    print(f"批量预测 {len(batch_queries)} 个查询...")
    
    # 执行批量预测
    results = system.batch_predict(batch_queries, return_details=False)
    
    # 显示结果
    print(f"\n📊 批量预测结果:")
    strategy_count = {}
    
    for i, (query, result) in enumerate(zip(batch_queries, results), 1):
        print(f"\n{i}. {query}")
        if result['prediction']:
            print(f"   预测: {' -> '.join(result['prediction'])}")
            print(f"   置信度: {result['confidence']:.3f}")
            print(f"   策略: {result['strategy_used']}")
            
            # 统计策略使用
            strategy = result['strategy_used']
            strategy_count[strategy] = strategy_count.get(strategy, 0) + 1
        else:
            print(f"   预测失败: {result.get('reasoning', '未知错误')}")
    
    # 显示策略统计
    print(f"\n📈 策略使用统计:")
    for strategy, count in strategy_count.items():
        strategy_name = {
            'vector_retrieval': '向量检索',
            'model_classification': '模型分类',
            'vector_retrieval_fallback': '向量检索(回退)'
        }.get(strategy, strategy)
        print(f"  {strategy_name}: {count} 次")

def example_evaluation():
    """评估示例"""
    print("\n" + "=" * 60)
    print("📊 系统评估示例")
    print("=" * 60)
    
    system = MathClassificationSystem()
    
    print("开始系统评估...")
    results = system.evaluate_system()
    
    if 'error' in results:
        print(f"❌ 评估失败: {results['error']}")
        return
    
    print(f"\n📈 评估结果摘要:")
    print(f"测试数据量: {results['test_data_size']}")
    print(f"相似度阈值: {results['similarity_threshold']}")
    
    # 显示各方法的性能
    methods = ['vector_retrieval', 'model_classification', 'hybrid_system']
    method_names = ['向量检索', '模型分类', '混合系统']
    
    for method, name in zip(methods, method_names):
        if method in results:
            method_result = results[method]
            accuracy = method_result['accuracy']
            correct = method_result['correct']
            total = method_result['total']
            print(f"{name:8}: {accuracy:.3f} ({correct}/{total})")
    
    # 显示混合系统的策略使用情况
    if 'hybrid_system' in results and 'strategy_usage' in results['hybrid_system']:
        usage = results['hybrid_system']['strategy_usage']
        print(f"\n🔧 混合系统策略使用:")
        print(f"  向量检索: {usage.get('vector_retrieval', 0)} 次")
        print(f"  模型分类: {usage.get('model_classification', 0)} 次")
        print(f"  失败: {usage.get('failed', 0)} 次")

def main():
    """主函数"""
    print("🧮 数学分类系统 - 使用示例")
    
    try:
        # 基础使用示例
        example_basic_usage()
        
        # 阈值对比示例
        example_threshold_comparison()
        
        # 批量预测示例
        example_batch_prediction()
        
        # 评估示例
        example_evaluation()
        
        print("\n" + "=" * 60)
        print("✅ 所有示例运行完成!")
        print("💡 提示: 使用 'python main.py interactive' 进入交互模式")
        print("=" * 60)
        
    except KeyboardInterrupt:
        print("\n👋 示例被用户中断")
    except Exception as e:
        print(f"\n❌ 示例运行出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
