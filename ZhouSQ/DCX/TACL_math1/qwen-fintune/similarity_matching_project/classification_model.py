"""
分类模型模块 - 基于Qwen3-4B的微调分类器
"""

import torch
from transformers import AutoTokenizer, AutoModelForSequenceClassification, Trainer, TrainingArguments
from transformers import DataCollatorWithPadding
from torch.utils.data import Dataset
import numpy as np
from typing import List, Tuple
import json
import os
from sklearn.preprocessing import LabelEncoder
from sklearn.metrics import accuracy_score
import config

# LoRA相关导入
try:
    from peft import LoraConfig, get_peft_model, TaskType, PeftModel
    PEFT_AVAILABLE = True
except ImportError:
    PEFT_AVAILABLE = False
    print("警告: peft库未安装，将使用全量微调")

class MathDataset(Dataset):
    def __init__(self, texts: List[str], labels: List[List[str]], tokenizer, max_length: int = 512):
        self.texts = texts
        self.labels = labels
        self.tokenizer = tokenizer
        self.max_length = max_length
        
        # 将层次标签转换为字符串
        self.label_strings = [" -> ".join(label) for label in labels]
        
        # 创建标签编码器
        self.label_encoder = LabelEncoder()
        self.encoded_labels = self.label_encoder.fit_transform(self.label_strings)
        
    def __len__(self):
        return len(self.texts)
    
    def __getitem__(self, idx):
        text = str(self.texts[idx])
        
        encoding = self.tokenizer(
            text,
            truncation=True,
            padding='max_length',
            max_length=self.max_length,
            return_tensors='pt'
        )
        
        return {
            'input_ids': encoding['input_ids'].flatten(),
            'attention_mask': encoding['attention_mask'].flatten(),
            'labels': torch.tensor(self.encoded_labels[idx], dtype=torch.long)
        }

class ClassificationModel:
    def __init__(self, model_path: str = None):
        self.model_path = model_path or config.QWEN_MODEL_PATH
        self.device = torch.device(config.DEVICE)
        self.tokenizer = None
        self.model = None
        self.label_encoder = None
        self.max_length = 512
        
        # 训练参数
        self.training_args = None
        self.trainer = None
        
    def load_model(self, num_labels: int):
        """加载预训练模型"""
        try:
            print(f"正在加载分类模型: {self.model_path}")
            self.tokenizer = AutoTokenizer.from_pretrained(self.model_path, trust_remote_code=True)

            # 添加pad_token如果不存在
            if self.tokenizer.pad_token is None:
                self.tokenizer.pad_token = self.tokenizer.eos_token
                self.tokenizer.pad_token_id = self.tokenizer.eos_token_id

            # 准备模型加载参数
            model_kwargs = {
                "num_labels": num_labels,
                "trust_remote_code": True,
                "pad_token_id": self.tokenizer.pad_token_id,
            }

            # 多GPU配置
            if config.USE_MULTI_GPU and config.AUTO_DEVICE_MAP:
                import torch
                if torch.cuda.device_count() > 1:
                    print(f"检测到 {torch.cuda.device_count()} 个GPU，使用自动设备映射")
                    model_kwargs["device_map"] = "auto"
                    model_kwargs["torch_dtype"] = torch.float16  # 使用半精度节省内存
                    # 不手动移动到设备，让device_map自动处理
                    self.device = None
                else:
                    print("只有一个GPU，使用单GPU模式")

            self.model = AutoModelForSequenceClassification.from_pretrained(
                self.model_path,
                **model_kwargs
            )

            # 应用LoRA配置
            if config.USE_LORA and PEFT_AVAILABLE:
                print("应用LoRA配置...")
                lora_config = LoraConfig(
                    task_type=TaskType.SEQ_CLS,
                    r=config.LORA_R,
                    lora_alpha=config.LORA_ALPHA,
                    lora_dropout=config.LORA_DROPOUT,
                    target_modules=config.LORA_TARGET_MODULES,
                )
                self.model = get_peft_model(self.model, lora_config)
                self.model.print_trainable_parameters()
                print("LoRA配置应用成功!")
            else:
                if config.USE_LORA:
                    print("警告: peft库不可用，使用全量微调")
                else:
                    print("使用全量微调")

            # 确保模型配置中有正确的pad_token_id
            if hasattr(self.model.config, 'pad_token_id'):
                self.model.config.pad_token_id = self.tokenizer.pad_token_id

            # 只在非device_map模式下手动移动到设备
            if self.device is not None:
                self.model.to(self.device)

            print("分类模型加载成功!")

        except Exception as e:
            print(f"加载分类模型失败: {e}")
            raise e
    
    def prepare_training_data(self, train_texts: List[str], train_labels: List[List[str]],
                             val_texts: List[str], val_labels: List[List[str]]) -> Tuple[MathDataset, MathDataset]:
        """准备训练数据 - 使用现有的训练集和验证集"""

        # 创建数据集
        train_dataset = MathDataset(train_texts, train_labels, self.tokenizer, self.max_length)
        val_dataset = MathDataset(val_texts, val_labels, self.tokenizer, self.max_length)

        # 保存标签编码器
        self.label_encoder = train_dataset.label_encoder

        return train_dataset, val_dataset
    
    def _calculate_steps_per_epochs(self, epochs: int, train_dataset_size: int = None) -> int:
        """计算指定epoch数对应的步数"""
        if train_dataset_size is None:
            # 使用默认的训练集大小估算
            train_dataset_size = 48211  # 从数据加载器获取的大小

        effective_batch_size = config.TRAIN_BATCH_SIZE * config.GRADIENT_ACCUMULATION_STEPS
        steps_per_epoch = train_dataset_size // effective_batch_size
        return steps_per_epoch * epochs

    def setup_training(self, output_dir: str = None, train_dataset_size: int = None):
        """设置训练参数"""
        output_dir = output_dir or os.path.join(config.OUTPUT_DIR, "classification_model")

        # 存储训练集大小用于步数计算
        self.train_dataset_size = train_dataset_size
        
        self.training_args = TrainingArguments(
            output_dir=output_dir,
            num_train_epochs=config.TRAIN_EPOCHS,
            per_device_train_batch_size=config.TRAIN_BATCH_SIZE,
            per_device_eval_batch_size=config.EVAL_BATCH_SIZE,
            gradient_accumulation_steps=config.GRADIENT_ACCUMULATION_STEPS,
            warmup_steps=config.WARMUP_STEPS,
            weight_decay=config.WEIGHT_DECAY,
            learning_rate=config.LEARNING_RATE,
            max_grad_norm=config.MAX_GRAD_NORM,
            logging_dir=os.path.join(output_dir, 'logs'),
            logging_steps=config.LOGGING_STEPS,
            # 使用统一的策略配置
            eval_strategy=config.EVAL_STRATEGY,
            save_strategy=config.SAVE_STRATEGY,
            # 根据策略计算对应的steps
            save_steps=self._calculate_steps_per_epochs(config.SAVE_EPOCHS, self.train_dataset_size) if config.SAVE_STRATEGY == "epoch" else config.SAVE_STEPS,
            eval_steps=self._calculate_steps_per_epochs(config.EVAL_EPOCHS, self.train_dataset_size) if config.EVAL_STRATEGY == "epoch" else config.EVAL_STEPS,
            save_total_limit=config.SAVE_TOTAL_LIMIT,
            load_best_model_at_end=True,
            metric_for_best_model="eval_accuracy",
            greater_is_better=True,
            report_to=None,  # 禁用wandb等
            # 内存优化设置
            bf16=False,  # 暂时禁用混合精度训练
            fp16=False,  # 禁用fp16避免梯度缩放问题
            dataloader_pin_memory=False,
            remove_unused_columns=True,
            # 多GPU训练设置
            ddp_find_unused_parameters=False if config.USE_MULTI_GPU else False,
        )
    
    def compute_metrics(self, eval_pred):
        """计算评估指标"""
        predictions, labels = eval_pred
        predictions = np.argmax(predictions, axis=1)
        
        accuracy = accuracy_score(labels, predictions)
        
        return {
            'accuracy': accuracy,
        }
    
    def train(self, train_dataset: MathDataset, val_dataset: MathDataset):
        """训练模型"""
        if self.model is None:
            raise ValueError("模型未加载，请先调用load_model()")
        
        # 设置训练参数
        self.setup_training(train_dataset_size=len(train_dataset.texts))
        
        # 创建数据整理器
        data_collator = DataCollatorWithPadding(tokenizer=self.tokenizer)
        
        # 创建训练器
        self.trainer = Trainer(
            model=self.model,
            args=self.training_args,
            train_dataset=train_dataset,
            eval_dataset=val_dataset,
            processing_class=self.tokenizer,  # 使用新的参数名
            data_collator=data_collator,
            compute_metrics=self.compute_metrics,
        )
        
        print("开始训练...")
        self.trainer.train()
        
        # 保存最终模型
        self.trainer.save_model()
        self.tokenizer.save_pretrained(self.training_args.output_dir)
        
        # 保存标签编码器
        label_encoder_path = os.path.join(self.training_args.output_dir, "label_encoder.json")
        label_mapping = {
            'classes_': self.label_encoder.classes_.tolist()
        }
        with open(label_encoder_path, 'w', encoding='utf-8') as f:
            json.dump(label_mapping, f, ensure_ascii=False, indent=2)
        
        print("训练完成!")
    
    def load_trained_model(self, model_dir: str):
        """加载已训练的模型"""
        try:
            print(f"正在加载已训练的模型: {model_dir}")

            self.tokenizer = AutoTokenizer.from_pretrained(model_dir, trust_remote_code=True)

            # 检查是否是LoRA模型
            adapter_config_path = os.path.join(model_dir, "adapter_config.json")
            if os.path.exists(adapter_config_path) and PEFT_AVAILABLE:
                print("检测到LoRA模型，正在加载...")
                # 先加载基础模型
                base_model = AutoModelForSequenceClassification.from_pretrained(
                    self.model_path, trust_remote_code=True
                )
                # 加载LoRA适配器
                self.model = PeftModel.from_pretrained(base_model, model_dir)
                print("LoRA模型加载成功!")
            else:
                # 加载全量微调模型
                self.model = AutoModelForSequenceClassification.from_pretrained(model_dir, trust_remote_code=True)
                print("全量微调模型加载成功!")

            self.model.to(self.device)
            self.model.eval()

            # 加载标签编码器
            label_encoder_path = os.path.join(model_dir, "label_encoder.json")
            with open(label_encoder_path, 'r', encoding='utf-8') as f:
                label_mapping = json.load(f)

            self.label_encoder = LabelEncoder()
            self.label_encoder.classes_ = np.array(label_mapping['classes_'])

            print("已训练模型加载成功!")

        except Exception as e:
            print(f"加载已训练模型失败: {e}")
            raise e
    
    def predict(self, texts: List[str]) -> List[Tuple[List[str], float]]:
        """预测文本标签"""
        if self.model is None or self.label_encoder is None:
            raise ValueError("模型未加载，请先调用load_model()或load_trained_model()")
        
        self.model.eval()
        predictions = []
        
        with torch.no_grad():
            for text in texts:
                # 编码文本
                encoding = self.tokenizer(
                    text,
                    truncation=True,
                    padding='max_length',
                    max_length=self.max_length,
                    return_tensors='pt'
                ).to(self.device)
                
                # 预测
                outputs = self.model(**encoding)
                logits = outputs.logits
                probabilities = torch.softmax(logits, dim=-1)
                
                # 获取最高概率的预测
                predicted_class_id = torch.argmax(probabilities, dim=-1).item()
                confidence = probabilities[0][predicted_class_id].item()
                
                # 解码标签
                predicted_label_string = self.label_encoder.inverse_transform([predicted_class_id])[0]
                predicted_label = predicted_label_string.split(" -> ")
                
                predictions.append((predicted_label, confidence))
        
        return predictions
    
    def predict_single(self, text: str) -> Tuple[List[str], float]:
        """预测单个文本"""
        results = self.predict([text])
        return results[0] if results else ([], 0.0)
