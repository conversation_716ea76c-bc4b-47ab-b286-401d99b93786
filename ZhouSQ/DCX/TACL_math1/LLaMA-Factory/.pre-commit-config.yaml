repos:
-   repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v5.0.0
    hooks:
    -   id: check-ast
    -   id: check-added-large-files
        args: ['--maxkb=25000']
    -   id: check-merge-conflict
    -   id: check-yaml
    -   id: debug-statements
    -   id: end-of-file-fixer
    -   id: trailing-whitespace
        args: [--markdown-linebreak-ext=md]
    -   id: no-commit-to-branch
        args: ['--branch', 'main']

-   repo: https://github.com/asottile/pyupgrade
    rev: v3.17.0
    hooks:
    -   id: pyupgrade
        args: [--py38-plus]

-   repo: https://github.com/astral-sh/ruff-pre-commit
    rev: v0.6.9
    hooks:
    -   id: ruff
        args: [--fix]
    -   id: ruff-format
