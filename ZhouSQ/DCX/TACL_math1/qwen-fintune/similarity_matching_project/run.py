#!/usr/bin/env python3
"""
统一运行控制器 - 管理所有系统运行模式
"""

import argparse
import sys
import os
import json
import logging
from pathlib import Path
from datetime import datetime
from typing import Optional, List, Dict, Any

# 设置环境变量减少不必要的日志
os.environ["TOKENIZERS_PARALLELISM"] = "false"
os.environ["TRANSFORMERS_VERBOSITY"] = "warning"
os.environ["DATASETS_VERBOSITY"] = "warning"

# 添加当前目录到Python路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

import config

# =============================================================================
# 核心功能模块
# =============================================================================

class SystemController:
    """系统控制器 - 统一管理所有运行模式"""

    def __init__(self):
        self.setup_logging()
        self.logger = logging.getLogger(__name__)

    def setup_logging(self):
        """设置日志系统"""
        logging.basicConfig(
            level=getattr(logging, config.LOG_LEVEL),
            format=config.LOG_FORMAT,
            handlers=[
                logging.StreamHandler(sys.stdout),
                logging.FileHandler(config.LOG_FILE, encoding='utf-8')
            ]
        )

    def validate_environment(self) -> bool:
        """验证运行环境"""
        self.logger.info("检查运行环境...")

        # 验证配置
        config_errors = config.validate_config()
        if config_errors:
            self.logger.error("配置验证失败:")
            for error in config_errors:
                self.logger.error(f"  - {error}")
            return False

        # 检查依赖包
        required_packages = [
            'torch', 'transformers', 'numpy',
            'tqdm', 'datasets', 'pandas', 'peft'
        ]

        missing_packages = []
        for package in required_packages:
            try:
                __import__(package.replace('-', '_'))
            except ImportError:
                missing_packages.append(package)

        if missing_packages:
            self.logger.error(f"缺少依赖包: {', '.join(missing_packages)}")
            self.logger.error("请运行: pip install -r requirements.txt")
            return False

        self.logger.info("环境验证通过")
        return True

    def show_system_info(self):
        """显示系统信息"""
        print("=" * 80)
        print("🚀 混合匹配系统 - 统一控制台")
        print("=" * 80)
        print(f"📍 项目路径: {config.PROJECT_ROOT}")
        print(f"🤖 嵌入模型: {config.EMBEDDING_MODEL_PATH}")
        print(f"🧠 分类模型: {config.CLASSIFICATION_MODEL_PATH}")
        print(f"📊 数据目录: {config.DATA_DIR}")
        print(f"💾 输出目录: {config.OUTPUT_DIR}")
        print(f"🔧 设备: {config.DEVICE}")
        print(f"⚙️  默认策略: {config.DEFAULT_STRATEGY}")
        print(f"🎯 LoRA微调: {'启用' if config.USE_LORA else '禁用'}")

        print("\n📋 可用运行模式:")
        mode_descriptions = {
            "info": "显示系统信息",
            "setup": "环境检查和设置",
            "build": "构建相似度知识库",
            "train": "训练分类模型",
            "predict": "单次预测模式",
            "batch": "批量预测模式",
            "eval": "模型评估",
            "interactive": "交互式预测",
            "add-data": "增量添加数据",
            "export": "导出模型",
            "clean": "清理缓存"
        }

        for mode in config.AVAILABLE_MODES:
            desc = mode_descriptions.get(mode, "未知模式")
            print(f"  • {mode:<12} - {desc}")

        print("\n💡 使用示例:")
        print("  python run.py --mode build                    # 构建知识库")
        print("  python run.py --mode train --use-lora         # LoRA训练")
        print("  python run.py --mode eval --strategy auto     # 自动策略评估")
        print("  python run.py --mode interactive              # 交互模式")
        print("=" * 80)

    def build_knowledge_base(self, use_cache: bool = True, force_rebuild: bool = False):
        """构建相似度知识库"""
        self.logger.info("开始构建相似度知识库...")

        from similarity_matcher import SimilarityMatcher
        matcher = SimilarityMatcher()

        if force_rebuild:
            use_cache = False
            self.logger.info("强制重建知识库，忽略缓存")

        matcher.build_knowledge_base(use_cache=use_cache)
        self.logger.info("相似度知识库构建完成")
        return matcher

    def train_classification_model(self, force_retrain: bool = False, skip_similarity: bool = False):
        """训练分类模型"""
        self.logger.info("开始训练分类模型...")

        if skip_similarity:
            # 直接训练分类模型，不构建相似度知识库
            from classification_model import ClassificationModel
            from data_loader import DataLoader

            data_loader = DataLoader()
            data_loader.load_all_data()

            # 加载训练数据和验证数据
            train_texts, train_labels = data_loader.create_training_dataset()
            val_texts, val_labels = data_loader.get_doc_tokens_and_labels(data_loader.val_data or [])

            # 如果没有验证集，从训练集分割一部分
            if not val_texts:
                self.logger.info("未找到验证集，从训练集分割20%作为验证集")
                split_idx = int(len(train_texts) * 0.8)
                val_texts = train_texts[split_idx:]
                val_labels = train_labels[split_idx:]
                train_texts = train_texts[:split_idx]
                train_labels = train_labels[:split_idx]

            # 获取唯一标签数量
            unique_labels = set()
            for label_list in train_labels:
                unique_labels.add(" -> ".join(label_list))
            for label_list in val_labels:
                unique_labels.add(" -> ".join(label_list))
            num_labels = len(unique_labels)

            self.logger.info(f"训练数据: {len(train_texts)} 个样本")
            self.logger.info(f"验证数据: {len(val_texts)} 个样本")
            self.logger.info(f"唯一标签数量: {num_labels}")

            # 初始化分类模型
            classification_model = ClassificationModel()
            classification_model.load_model(num_labels)

            # 准备训练数据
            train_dataset, val_dataset = classification_model.prepare_training_data(
                train_texts, train_labels, val_texts, val_labels)

            # 训练
            classification_model.train(train_dataset, val_dataset)

            self.logger.info("分类模型训练完成")
            return classification_model
        else:
            # 使用混合匹配器训练（包含相似度知识库）
            from hybrid_matcher import HybridMatcher
            matcher = HybridMatcher()

            # 先构建相似度知识库
            matcher.build_similarity_knowledge_base(use_cache=True)

            # 训练分类模型
            matcher.train_classification_model(force_retrain=force_retrain)

            self.logger.info("分类模型训练完成")
            return matcher

    def run_prediction(self, query: str, strategy: str = None):
        """运行单次预测"""
        strategy = strategy or config.DEFAULT_STRATEGY
        self.logger.info(f"运行预测，策略: {strategy}")

        from hybrid_matcher import HybridMatcher
        matcher = HybridMatcher()

        # 加载系统
        matcher.build_similarity_knowledge_base(use_cache=True)
        matcher.train_classification_model(force_retrain=False)

        # 预测
        result = matcher.predict_with_strategy(query, strategy=strategy)

        print(f"\n🔍 查询: {query}")
        print(f"📊 策略: {result['strategy_used']}")
        print(f"🎯 预测: {result['final_prediction']}")
        print(f"📈 置信度: {result['confidence']:.4f}")
        print(f"💭 推理: {result['reasoning']}")

        return result

    def run_batch_prediction(self, input_file: str, output_file: str = None, strategy: str = None):
        """运行批量预测"""
        strategy = strategy or config.DEFAULT_STRATEGY
        self.logger.info(f"运行批量预测，输入: {input_file}")

        # 加载输入数据
        if not os.path.exists(input_file):
            self.logger.error(f"输入文件不存在: {input_file}")
            return False

        with open(input_file, 'r', encoding='utf-8') as f:
            input_data = json.load(f)

        queries = [item.get('query', item.get('doc_token', '')) for item in input_data]

        from hybrid_matcher import HybridMatcher
        matcher = HybridMatcher()

        # 加载系统
        matcher.build_similarity_knowledge_base(use_cache=True)
        matcher.train_classification_model(force_retrain=False)

        # 批量预测
        results = matcher.batch_predict(queries, strategy=strategy)

        # 保存结果
        if output_file is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_file = config.RESULTS_DIR / f"batch_prediction_{timestamp}.json"

        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)

        self.logger.info(f"批量预测完成，结果保存到: {output_file}")
        return True

    def run_evaluation(self, strategy: str = None, save_report: bool = True):
        """运行模型评估"""
        strategy = strategy or config.DEFAULT_STRATEGY
        self.logger.info(f"开始模型评估，策略: {strategy}")

        from hybrid_matcher import HybridMatcher
        matcher = HybridMatcher()

        # 加载系统
        matcher.build_similarity_knowledge_base(use_cache=True)
        matcher.train_classification_model(force_retrain=False)

        # 评估
        evaluation_result = matcher.evaluate_on_test_set(strategy=strategy)

        # 显示结果
        print(f"\n📊 评估结果 (策略: {strategy}):")
        print(f"🎯 准确率: {evaluation_result['accuracy']:.4f}")
        print(f"✅ 正确预测: {evaluation_result['correct_predictions']}")
        print(f"📈 总预测数: {evaluation_result['total_predictions']}")
        print(f"📋 策略使用统计: {evaluation_result['strategy_usage']}")

        # 保存报告
        if save_report:
            report_file = matcher.save_evaluation_report(evaluation_result)
            self.logger.info(f"评估报告已保存: {report_file}")

        return evaluation_result

    def run_interactive_mode(self, strategy: str = None):
        """运行交互式预测模式"""
        strategy = strategy or config.DEFAULT_STRATEGY
        print(f"\n🚀 交互式预测模式 (策略: {strategy})")
        print("输入查询文本进行预测，输入 'quit' 退出，输入 'help' 查看帮助")

        from hybrid_matcher import HybridMatcher
        matcher = HybridMatcher()

        # 加载系统
        print("正在加载系统...")
        matcher.build_similarity_knowledge_base(use_cache=True)
        matcher.train_classification_model(force_retrain=False)
        print("系统加载完成！\n")

        while True:
            try:
                query = input("🔍 请输入查询: ").strip()

                if query.lower() == 'quit':
                    print("👋 再见！")
                    break
                elif query.lower() == 'help':
                    print("\n💡 帮助信息:")
                    print("  • 输入任何文本进行预测")
                    print("  • 输入 'quit' 退出程序")
                    print("  • 输入 'strategy <策略名>' 切换策略")
                    print("  • 可用策略: similarity, classification, auto, ensemble\n")
                    continue
                elif query.startswith('strategy '):
                    new_strategy = query.split(' ', 1)[1].strip()
                    if new_strategy in config.EVAL_STRATEGIES:
                        strategy = new_strategy
                        print(f"✅ 策略已切换为: {strategy}\n")
                    else:
                        print(f"❌ 无效策略，可用策略: {', '.join(config.EVAL_STRATEGIES)}\n")
                    continue
                elif not query:
                    continue

                # 预测
                result = matcher.predict_with_strategy(query, strategy=strategy)

                print(f"\n📊 预测结果:")
                print(f"  🎯 预测标签: {result['final_prediction']}")
                print(f"  📈 置信度: {result['confidence']:.4f}")
                print(f"  🔧 使用策略: {result['strategy_used']}")
                print(f"  💭 推理过程: {result['reasoning']}")
                print()

            except KeyboardInterrupt:
                print("\n👋 程序被中断，再见！")
                break
            except Exception as e:
                print(f"❌ 预测出错: {e}")

    def add_incremental_data(self, data_file: str, retrain: bool = False):
        """增量添加数据"""
        self.logger.info(f"增量添加数据: {data_file}")

        if not os.path.exists(data_file):
            self.logger.error(f"数据文件不存在: {data_file}")
            return False

        from hybrid_matcher import HybridMatcher
        matcher = HybridMatcher()

        # 加载现有系统
        matcher.build_similarity_knowledge_base(use_cache=True)
        if retrain:
            matcher.train_classification_model(force_retrain=False)

        # 添加新数据
        matcher.add_new_data_from_file(data_file, retrain_classification=retrain)

        self.logger.info("增量数据添加完成")
        return True

    def clean_cache(self, confirm: bool = False):
        """清理缓存"""
        if not confirm:
            response = input("⚠️  确定要清理所有缓存吗？这将删除已计算的嵌入向量 (y/N): ")
            if response.lower() != 'y':
                print("❌ 操作已取消")
                return False

        import shutil

        cache_dirs = [
            config.EMBEDDINGS_CACHE_DIR,
            config.MODELS_DIR / "classification_model"
        ]

        for cache_dir in cache_dirs:
            if cache_dir.exists():
                shutil.rmtree(cache_dir)
                self.logger.info(f"已清理缓存目录: {cache_dir}")

        # 重新创建目录
        config.init_directories()

        print("✅ 缓存清理完成")
        return True

# =============================================================================
# 命令行参数解析和主函数
# =============================================================================

def create_parser():
    """创建命令行参数解析器"""
    parser = argparse.ArgumentParser(
        description="混合匹配系统 - 统一运行控制器",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
运行模式说明:
  info        显示系统信息和配置
  setup       检查环境和依赖
  build       构建相似度知识库
  train       训练分类模型
  predict     单次预测
  batch       批量预测
  eval        模型评估
  interactive 交互式预测
  add-data    增量添加数据
  export      导出模型
  clean       清理缓存

使用示例:
  python run.py --mode info
  python run.py --mode build --no-cache
  python run.py --mode train --use-lora
  python run.py --mode predict --query "你的问题"
  python run.py --mode eval --strategy auto
  python run.py --mode interactive
  python run.py --mode add-data --input new_data.json --retrain
        """
    )

    # 基本参数
    parser.add_argument(
        '--mode',
        choices=config.AVAILABLE_MODES,
        default=config.DEFAULT_MODE,
        help=f'运行模式 (默认: {config.DEFAULT_MODE})'
    )

    # 通用参数
    parser.add_argument('--device', choices=['cpu', 'cuda'], help='指定计算设备')
    parser.add_argument('--no-cache', action='store_true', help='不使用缓存')
    parser.add_argument('--force', action='store_true', help='强制执行操作')
    parser.add_argument('--verbose', action='store_true', help='详细输出')

    # 策略参数
    parser.add_argument(
        '--strategy',
        choices=config.EVAL_STRATEGIES,
        default=config.DEFAULT_STRATEGY,
        help=f'预测策略 (默认: {config.DEFAULT_STRATEGY})'
    )

    # 训练参数
    parser.add_argument('--use-lora', action='store_true', help='使用LoRA微调')
    parser.add_argument('--no-lora', action='store_true', help='不使用LoRA微调')
    parser.add_argument('--skip-similarity', action='store_true', help='跳过相似度知识库构建，仅训练分类模型')
    parser.add_argument('--epochs', type=int, help='训练轮数')
    parser.add_argument('--batch-size', type=int, help='批处理大小')
    parser.add_argument('--learning-rate', type=float, help='学习率')

    # 输入输出参数
    parser.add_argument('--input', type=str, help='输入文件路径')
    parser.add_argument('--output', type=str, help='输出文件路径')
    parser.add_argument('--query', type=str, help='预测查询文本')

    # 特殊参数
    parser.add_argument('--retrain', action='store_true', help='重新训练模型')
    parser.add_argument('--save-report', action='store_true', default=True, help='保存评估报告')

    return parser

def apply_args_to_config(args):
    """将命令行参数应用到配置"""
    if args.device:
        config.DEVICE = args.device

    if args.use_lora:
        config.USE_LORA = True
    elif args.no_lora:
        config.USE_LORA = False

    if args.epochs:
        config.TRAIN_EPOCHS = args.epochs

    if args.batch_size:
        config.BATCH_SIZE = args.batch_size
        config.TRAIN_BATCH_SIZE = args.batch_size

    if args.learning_rate:
        config.LEARNING_RATE = args.learning_rate

    if args.verbose:
        config.LOG_LEVEL = "DEBUG"

def main():
    """主函数 - 统一入口点"""
    parser = create_parser()
    args = parser.parse_args()

    # 应用命令行参数到配置
    apply_args_to_config(args)

    # 创建系统控制器
    controller = SystemController()

    try:
        # 根据模式执行相应功能
        if args.mode == 'info':
            controller.show_system_info()

        elif args.mode == 'setup':
            success = controller.validate_environment()
            if success:
                print("✅ 环境检查通过")
            else:
                print("❌ 环境检查失败")
            sys.exit(0 if success else 1)

        elif args.mode == 'build':
            if not controller.validate_environment():
                sys.exit(1)
            controller.build_knowledge_base(
                use_cache=not args.no_cache,
                force_rebuild=args.force
            )

        elif args.mode == 'train':
            if not controller.validate_environment():
                sys.exit(1)
            controller.train_classification_model(
                force_retrain=args.force,
                skip_similarity=args.skip_similarity
            )

        elif args.mode == 'predict':
            if not args.query:
                print("❌ 预测模式需要提供 --query 参数")
                sys.exit(1)
            if not controller.validate_environment():
                sys.exit(1)
            controller.run_prediction(args.query, args.strategy)

        elif args.mode == 'batch':
            if not args.input:
                print("❌ 批量预测模式需要提供 --input 参数")
                sys.exit(1)
            if not controller.validate_environment():
                sys.exit(1)
            controller.run_batch_prediction(args.input, args.output, args.strategy)

        elif args.mode == 'eval':
            if not controller.validate_environment():
                sys.exit(1)
            controller.run_evaluation(args.strategy, args.save_report)

        elif args.mode == 'interactive':
            if not controller.validate_environment():
                sys.exit(1)
            controller.run_interactive_mode(args.strategy)

        elif args.mode == 'add-data':
            if not args.input:
                print("❌ 增量数据模式需要提供 --input 参数")
                sys.exit(1)
            if not controller.validate_environment():
                sys.exit(1)
            controller.add_incremental_data(args.input, args.retrain)

        elif args.mode == 'clean':
            controller.clean_cache(confirm=args.force)

        else:
            print(f"❌ 未知模式: {args.mode}")
            parser.print_help()
            sys.exit(1)

    except KeyboardInterrupt:
        print("\n👋 程序被用户中断")
        sys.exit(1)
    except Exception as e:
        controller.logger.error(f"运行出错: {e}")
        if args.verbose:
            import traceback
            traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()
