"""
工具函数模块
"""

import json
import os
import numpy as np
from typing import List, Dict, Any
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime

def save_json(data: Any, filepath: str, ensure_ascii: bool = False):
    """保存数据为JSON文件"""
    os.makedirs(os.path.dirname(filepath), exist_ok=True)
    with open(filepath, 'w', encoding='utf-8') as f:
        json.dump(data, f, ensure_ascii=ensure_ascii, indent=2)

def load_json(filepath: str) -> Any:
    """加载JSON文件"""
    with open(filepath, 'r', encoding='utf-8') as f:
        return json.load(f)

def calculate_label_hierarchy_similarity(label1: List[str], label2: List[str]) -> float:
    """
    计算标签层次结构的相似度
    考虑标签的层次结构，越深层的匹配权重越高
    """
    if not label1 or not label2:
        return 0.0
    
    # 找到共同的前缀长度
    common_prefix_length = 0
    min_length = min(len(label1), len(label2))
    
    for i in range(min_length):
        if label1[i] == label2[i]:
            common_prefix_length += 1
        else:
            break
    
    # 计算相似度，考虑层次权重
    if common_prefix_length == 0:
        return 0.0
    
    # 权重递增：越深层的匹配权重越高
    weights = [i + 1 for i in range(common_prefix_length)]
    total_weight = sum(weights)
    max_possible_weight = sum([i + 1 for i in range(max(len(label1), len(label2)))])
    
    similarity = total_weight / max_possible_weight
    return similarity

def analyze_prediction_errors(evaluation_result: Dict) -> Dict:
    """分析预测错误"""
    detailed_results = evaluation_result['detailed_results']
    
    error_analysis = {
        'total_errors': 0,
        'error_types': {},
        'similarity_distribution': [],
        'error_examples': []
    }
    
    for result in detailed_results:
        if not result['is_correct']:
            error_analysis['total_errors'] += 1
            
            # 分析错误类型
            true_label = result['true_label']
            pred_label = result['predicted_label']
            
            # 计算标签层次相似度
            hierarchy_sim = calculate_label_hierarchy_similarity(true_label, pred_label)
            error_analysis['similarity_distribution'].append(hierarchy_sim)
            
            # 记录错误示例
            if len(error_analysis['error_examples']) < 10:
                error_analysis['error_examples'].append({
                    'query': result['test_text'][:200] + "...",
                    'true_label': true_label,
                    'predicted_label': pred_label,
                    'hierarchy_similarity': hierarchy_sim
                })
    
    return error_analysis

def plot_similarity_distribution(similarities: List[float], save_path: str = None):
    """绘制相似度分布图"""
    plt.figure(figsize=(10, 6))
    plt.hist(similarities, bins=50, alpha=0.7, edgecolor='black')
    plt.xlabel('Similarity Score')
    plt.ylabel('Frequency')
    plt.title('Distribution of Similarity Scores')
    plt.grid(True, alpha=0.3)
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"相似度分布图已保存到: {save_path}")
    else:
        plt.show()
    
    plt.close()

def create_evaluation_report(evaluation_result: Dict, output_dir: str):
    """创建评估报告"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    # 基本统计
    accuracy = evaluation_result['accuracy']
    total = evaluation_result['total_predictions']
    correct = evaluation_result['correct_predictions']
    
    # 错误分析
    error_analysis = analyze_prediction_errors(evaluation_result)
    
    # 创建报告
    report = {
        'timestamp': timestamp,
        'overall_performance': {
            'accuracy': accuracy,
            'total_predictions': total,
            'correct_predictions': correct,
            'error_count': error_analysis['total_errors']
        },
        'error_analysis': error_analysis,
        'summary': f"在{total}个测试样本中，正确预测了{correct}个，准确率为{accuracy:.4f}"
    }
    
    # 保存报告
    report_path = os.path.join(output_dir, f"evaluation_report_{timestamp}.json")
    save_json(report, report_path)
    
    print(f"评估报告已保存到: {report_path}")
    return report

def print_colored_text(text: str, color: str = 'white'):
    """打印彩色文本"""
    colors = {
        'red': '\033[91m',
        'green': '\033[92m',
        'yellow': '\033[93m',
        'blue': '\033[94m',
        'purple': '\033[95m',
        'cyan': '\033[96m',
        'white': '\033[97m',
        'end': '\033[0m'
    }
    
    if color in colors:
        print(f"{colors[color]}{text}{colors['end']}")
    else:
        print(text)

def format_label_hierarchy(labels: List[str]) -> str:
    """格式化标签层次结构显示"""
    if not labels:
        return "无标签"
    
    formatted = ""
    for i, label in enumerate(labels):
        indent = "  " * i
        formatted += f"{indent}└─ {label}\n"
    
    return formatted.rstrip()

def compare_labels(true_labels: List[str], pred_labels: List[str]) -> Dict:
    """比较两个标签层次结构"""
    comparison = {
        'exact_match': true_labels == pred_labels,
        'hierarchy_similarity': calculate_label_hierarchy_similarity(true_labels, pred_labels),
        'common_prefix_length': 0,
        'true_depth': len(true_labels),
        'pred_depth': len(pred_labels)
    }
    
    # 计算共同前缀长度
    min_length = min(len(true_labels), len(pred_labels))
    for i in range(min_length):
        if true_labels[i] == pred_labels[i]:
            comparison['common_prefix_length'] += 1
        else:
            break
    
    return comparison
